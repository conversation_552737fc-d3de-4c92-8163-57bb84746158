<?php
use <PERSON><PERSON>\Timber;
use <PERSON>ber\PostQuery;

/**
 * Template Name: Équipe
 */
$context = Timber::context();
$timber_post = Timber::get_post();

$limit    = empty($_GET['limit']) ? 999999 : $_GET['limit'];

$search = empty($_GET['search']) ? '' : $_GET['search'];
$post_type = get_field('post_type', $timber_post->ID);

// Check if current post type has posts, if not redirect to next available team page
$current_post_count = wp_count_posts($post_type)->publish;

if ($current_post_count === 0) {
    // Get team configuration
    $team_config = [
        'farm' => ['page_key' => 'team_farm', 'position' => 0],
        'researcher' => ['page_key' => 'team_researcher', 'position' => 1], 
        'coordinator' => ['page_key' => 'team_coordinator', 'position' => 2],
        'collaborator' => ['page_key' => 'team_collaborator', 'position' => 3]
    ];
    
    $current_position = $team_config[$post_type]['position'] ?? 0;
    
    // Find next available page with posts
    $positions_to_check = array_merge(
        range($current_position + 1, 3),
        range(0, $current_position - 1)
    );
    
    foreach ($positions_to_check as $position) {
        $check_post_type = array_search($position, array_column($team_config, 'position'));
        if ($check_post_type !== false) {
            $check_post_type = array_keys($team_config)[$check_post_type];
            if (wp_count_posts($check_post_type)->publish > 0) {
                $next_page = $team_config[$check_post_type]['page_key'];
                if (isset($context['static_pages'][$next_page])) {
                    wp_redirect($context['static_pages'][$next_page]->link);
                    exit;
                }
                break;
            }
        }
    }
}

$context['filters'] = array(
    'search' => true
);

$categories = get_terms([
    'taxonomy' => $post_type . '_type',
    'hide_empty' => true,
]);

$context['categories'] = [];

$count = 0;
// Get posts for each category
foreach ($categories as $category) {
    $args = [
        'post_type'      => $post_type,
        'post_status'    => 'publish',
        'orderby'        => 'title',
        'order'          => 'ASC',
        'tax_query'      => [
            [
                'taxonomy' => $post_type . '_type',
                'field'    => 'term_id',
                'terms'    => $category->term_id
            ]
        ]
    ];

    if (!empty($search)) {
        $args['s'] = $search;
    }
    
    $query = new WP_Query($args);
    
    $context['categories'][] = [
        'ID' => $category->term_id,
        'name' => $category->name,
        'count' => $query->found_posts,
        'posts' => new PostQuery($query)
    ];

    $count += $query->found_posts;
}

// Function to get singular/plural form based on count
$get_people_type = function($post_type, $count) {
    $singular_forms = match ($post_type) {
        'farm' => __('ferme', 'laboratoire-vivant'),
        'researcher' => __('scientifique', 'laboratoire-vivant'),
        'coordinator' => __('coordinateur', 'laboratoire-vivant'), 
        'collaborator' => __('collaborateur', 'laboratoire-vivant')
    };
    
    $plural_forms = match ($post_type) {
        'farm' => __('fermes', 'laboratoire-vivant'),
        'researcher' => __('scientifiques', 'laboratoire-vivant'),
        'coordinator' => __('coordinateurs', 'laboratoire-vivant'), 
        'collaborator' => __('collaborateurs', 'laboratoire-vivant')
    };
    
    return $count > 1 ? $plural_forms : $singular_forms;
};

$context['people_type'] = $get_people_type($post_type, $count);

$context['count'] = $count;
$context['post_type'] = $post_type;

$context['params'] = $_GET;
$context['post'] = $timber_post;
$context['url'] = '/ajax/team-list';
$context['limit'] = $limit;

// Render the page with action fields slider
Timber::render( array( 'pages/team-page.twig' ), $context );