<?php

$context = Timber::context();
$timber_post = Timber::get_post();
$context['post'] = $timber_post;

// Get post type object to access its name
$post_type_object = get_post_type_object($timber_post->post_type);
$context['post_type_name'] = $post_type_object && isset($post_type_object->labels->singular_name) ? $post_type_object->labels->singular_name : '';

// Get the correct taxonomy for researcher post type
$terms = get_the_terms($timber_post->ID, 'researcher_type');
$context['category'] = (is_array($terms) && !empty($terms)) ? $terms[0] : null;

$related_actions = get_field('related_actions', $timber_post->ID);
$context['related_actions'] = is_array($related_actions) ? $related_actions : [];


// Get all posts where current post ID is in relationships ACF field authors or collaborators
$related_posts = get_posts([
    'post_type' => 'post',
    'posts_per_page' => -1,
    'meta_query' => [
        'relation' => 'OR',
        'authors' => [
            'key' => 'authors',
            'value' => '"' . $timber_post->ID . '"',
            'compare' => 'LIKE'
        ],
        'collaborators' => [
            'key' => 'collaborators',
            'value' => '"' . $timber_post->ID . '"',
            'compare' => 'LIKE'
        ]
    ]
]);

$context['related_posts'] = $related_posts;

$context['gallery'] = get_field('gallery', $timber_post->ID);

// Get all researchers where they have same related_actions field
$related_researchers_args = [
    'post_type' => 'researcher',
    'posts_per_page' => -1,
    'post__not_in' => [$timber_post->ID]
];

$related_researchers_args['meta_query'] = [
    'relation' => 'OR'
];

if (is_array($context['related_actions'])) {
    foreach ($context['related_actions'] as $action) {
        if (is_object($action) && isset($action->ID)) {
            $related_researchers_args['meta_query'][] = [
                'key' => 'related_actions',
                'value' => '"' . $action->ID . '"',
                'compare' => 'LIKE'
            ];
        }
    }
}

$related_researchers = get_posts($related_researchers_args);
$context['same_action_posts'] = $related_researchers;

Timber::render( array( 'pages/singles/single-reseacher.twig' ), $context );
