{% extends 'base.twig' %}

{# {% block mouse_tracker %}
    {% embed 'partials/snippets/mouse-tracker.twig' %}
        {% block content %}
            <span class="">{{ __('Voir', 'laboratoire-vivant') }}</span>
        {% endblock %}
    {% endembed %}
{% endblock %} #}

{% block content %}

{% include 'partials/heroes/title-illustration-hero.twig' with {
    title: __('L’équipe', 'laboratoire-vivant'),
    illustration: '/assets/images/hero/team-illustration.svg',
    team: true,
    current_link: post.link
} %}

<div id="content" class="container">

        <div data-module-ajax-content>
            <div data-scroll data-ajax-content="dynamic-content" class="slide-in" id="ajax-content">
                {% include 'partials/loading.twig' %}

                <div class="header-wrapper grid">
                    <div class="col-12 col-t-lg-6">
                        <p class="count">{{ count }} {{ people_type }}</p>
                    </div>

                    <div class="col-12 col-t-lg-6">
                        <form id="ajax-form" data-filter="ajax-form">
                            <input type="hidden" id="ajax-settings" data-url="{{ url }}" data-limit="{{ limit ?: 20 }}">

                            <div class="filters-wrapper">
                                {% include 'partials/filters/filters.twig' with {
                                    search_placeholder: __('Chercher par nom', 'laboratoire-vivant'),
                                    search: filters.search,
                                    type: post_type
                                } %}
                            </div>
                        </form>
                    </div>
                </div>
                <div id="append-ajax" class="inner">
                    {% include 'partials/lists/team-list.twig' with {
                        categories: categories
                    } %}
                </div>
            </div>
        </div>
</div>

{% include 'partials/what-lvlc.twig' %}

{% endblock %}