{% extends 'base.twig' %}

{% block content %}

    {% include 'partials/heroes/single-team-hero.twig' with {
        link: static_pages.team_researcher.link,
        collaborator: post,
        related_actions: related_actions
    } %}

    <div class="container">
        {% include 'partials/team-content.twig' with {
            content: post.meta('content')
        } %}
    </div>

    {% if related_posts %}
        <div class="related-posts-section">
            <div class="container">
                <div class="related-posts-header">
                    <h2 class="title -small">{{ __('Publications', 'laboratoire-vivant') }}</h2>
                    <img src="{{ theme.link }}/assets/images/icons/team-posts-icon.svg" alt="Publications" class="icon">
                </div>
                <div id="append-ajax" class="related-posts posts-list inner">
                    <div class="grid">
                        {% include 'partials/lists/posts-list.twig' with { 
                            results: related_posts,
                        } %}
                    </div>

                    {% set actions_id = [] %}
                    {% for a in related_actions %}
                        {% set id = a.ID is defined ? a.ID : a %}
                        {% if id is not empty and id not in actions_id %}
                            {% set actions_id = actions_id|merge([id]) %}
                        {% endif %}
                    {% endfor %}
                    <a href="{{ static_pages.publications.link }}?action={{ actions_id|join('%2C') }}&category=all" class="link-more">
                        {{ __('Voir plus', 'laboratoire-vivant') }}
                        <span class='icon-plus'></span> 
                    </a>
                </div>
            </div>
        </div>
    {% endif %}

    {% if gallery %}
        <div class="gallery-section">
            <div class="container">
                <div class="gallery-header">
                    <h2 class="title -small">{{ __('La ferme en photos', 'laboratoire-vivant') }}</h2>
                    <img src="{{ theme.link }}/assets/images/icons/team-gallery-icon.svg" alt="La ferme en photos" class="icon">
                </div>
                <div class="gallery">
                    {% include 'partials/sliders/team-gallery-slider.twig' with {
                        slides: gallery,
                    } %}
                </div>
            </div>
        </div>
    {% endif %}

    {% if same_action_posts %}
        <div class="related-people-section">
            <div class="container">
                <div class="related-people-header">
                    <h2 class="title -small">{{ __('Les scientifiques qui travaillent sur les mêmes champs d\'action', 'laboratoire-vivant') }}</h2>
                </div>
                <div class="related-people">
                    {% include 'partials/sliders/team-people-slider.twig' with {
                        slides: same_action_posts,
                    } %}
                </div>
            </div>
        </div>
    {% endif %}

    {% include 'partials/team-footer.twig' %}

{% endblock %}