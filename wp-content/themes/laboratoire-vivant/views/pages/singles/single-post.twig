{% extends 'base.twig' %}

{% block content %}

    {% include 'partials/heroes/single-post-hero.twig' with {
        title: post.title,
        category: category,
        category_color: category_color,
        post: post
    } %}

    {% if post.thumbnail %}
    <div data-scroll class="post-thumbnail-wrapper fade-in container">
        <img class="post-thumbnail" src="{{ post.thumbnail.src('full') }}" alt="{{ post.title }}">
    </div>
    {% endif %}

    <div id="content" class="container -narrow single-post-content">
        <div data-scroll class="excerpt slide-in">
            {{post.excerpt}}
        </div>
        <div data-scroll class="meta-content slide-in">
            {% if post.meta('authors') %}
                {% include 'partials/cards/authors-card.twig' with {
                    authors: post.meta('authors')
                } %}
            {% endif %}
            <div class="meta-date">
                <span>{{ post.date }}</span>
            </div>
        </div>
        <div data-scroll class="content gutenberg slide-in">
            {{post.content}}
        </div>
    </div>

    {% if post.meta('documents') | length > 0 %}
        <div class="container -bigger">
            <div data-scroll class="documents slide-in">
                {% for document in post.meta('documents') %}
                    <a  href="{{ document.file.url }}" class="document -clean -h3 -small" target="_blank">
                        <div class="first-part">
                        <span class="icon-file"></span>
                        {% if document.title %}
                            {{ __('Document ' ~ document.title ~ ' dont traite l\'article', 'laboratoire-vivant') }}
                        {% else %}
                            {{ __('Document ' ~ document.file.title ~ ' dont traite l\'article', 'laboratoire-vivant') }}
                        {% endif %}
                        </div>
                        <span class="icon-arrow-link-download"></span>
                    </a>
                {% endfor %}
            </div>
        </div>
    {% endif %}

    <div class="authors-list">
        <div class="container -bigger">
            <div class="main-authors">
                <h2 data-scroll class="title slide-in">
                    {% if post.meta('authors')|length > 1 %}
                        {{ __('À propos des auteur(e)s', 'laboratoire-vivant') }}
                    {% else %}
                        {{ __('À propos de l’auteur(e)', 'laboratoire-vivant') }}
                    {% endif %}
                </h2>
                <div class="authors-list-content">
                    {% for author in post.meta('authors') %}
                        {% set author = get_post(author) %}
                        <div data-scroll class="author slide-in">
                            <div class="author-image">
                                <img src="{{ author.thumbnail.src }}" alt="{{ author.post_title }}">
                            </div>
                            <div class="author-content">
                                <div class="author-main">
                                    <h3 class="author-name">{{ author.post_title }}</h3>
                                    <div class="author-description">{{ author.meta('subtitle') }}</div>
                                    <a href="{{ author.link }}" class="author-link">
                                        {{ __('Découvrir l’auteur(e)', 'laboratoire-vivant') }} <span class="icon-arrow-link"></span>
                                    </a>
                                </div>
                                {% if author.meta('related_actions') %}
                                <div class="author-links">
                                    <h4 class="author-links-title">
                                        {{ __('Ses thématiques de recherche dans le cadre du Labo vivant :', 'laboratoire-vivant') }}
                                    </h4>
                                    <div class="author-links-list">
                                        {% for related_action in author.meta('related_actions') %}
                                            {% set related_action = get_post(related_action) %}
                                            {% set link = related_action.link %}
                                            <a href="{{ link }}" class="author-link">
                                                <span class="icon-arrow-link"></span>
                                                {{ related_action.post_title }}
                                            </a>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
            {% if post.meta('collaborators') %}
            <div class="other-authors">
                <h2 data-scroll class="title slide-in">
                    {{ __('Collaborateur(trice)s de l’article', 'laboratoire-vivant') }}
                </h2>
                <div  class="other-authors-list">
                    {% for collaborator in post.meta('collaborators') %}
                        {% set collaborator = get_post(collaborator) %}
                        {% include 'partials/cards/collaborator-card.twig' with {
                            collaborator: collaborator,
                            link: collaborator.link
                        } %}
                    {% endfor %}
                    </div>
                </div>
            {% endif %}
        </div>
    </div>

    {% if slides %}
        <div class="slider-wrapper container -bigger">
            <h2 class="title wp-block-heading">{{ __('À lire aussi', 'laboratoire-vivant') }}</h2>
            {% include 'partials/sliders/posts-slider.twig' with {
                slides: slides
            } %}
        </div>
    {% endif %}

    {% include 'partials/what-lvlc.twig' %}
{% endblock %}