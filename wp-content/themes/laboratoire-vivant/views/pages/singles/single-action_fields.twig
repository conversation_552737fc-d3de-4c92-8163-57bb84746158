{% extends 'base.twig' %}

{% block content %}

    {% include 'partials/heroes/single-action_fields-hero.twig' with {
        title: post.title,
        color: post.meta('color'),
        illustration: post.meta('illustration')
    } %}

    <div id="content" class="gutenberg-main-content container -bigger grid">
        {% if anchors %}
        <aside data-scroll class="slide-in-left navigation-ctn col-12 col-t-lg-3">
            <div class="anchors-ctn" id="anchors-container" data-module-anchors>
                {% for index, anchor in anchors %}
                    <a href="#{{ index }}" class="anchor">{{ anchor }}</a>
                {% endfor %}
            </div>
        </aside>
        {% endif %}
        <div data-scroll id="gutenberg-content" class="gutenberg content col-12 slide-in {% if anchors %}col-t-lg-9{% else %}-full{% endif %}">
            {{post.content}}
        </div>
    </div>

    <div class="secondary-content">
        <div class="container -bigger">
            {% if primary_researchers %}
            <div class="researchers-ctn">
                <h2 data-scroll class="title slide-in">{{ __('Équipe de recherche', 'laboratoire-vivant') }}</h2>
                <div class="researchers-list grid">
                    {% if primary_researchers %}
                        <div class="researcher-item -primary col-12 col-t-lg-6">
                            <h3 data-scroll class="researcher-title -small slide-in">{{ primary_researchers.title }}</h3>
                            <div data-scroll class="slide-in">
                                {% for researcher in primary_researchers.researchers %}
                                    {% set researcher = get_post(researcher) %}
                                    {% include 'partials/cards/collaborator-card.twig' with {
                                        link: researcher.link,
                                        collaborator: researcher
                                    } %}
                                {% endfor %}
                            </div>
                        </div>
                    {% endif %}


                    <div class="researcher-item -secondary col-12 col-t-lg-6">
                        {% for researcher_group in secondary_researchers %}
                            {% if researcher_group.researchers %}
                                <h3 data-scroll class="researcher-title -small slide-in">{{ researcher_group.title }}</h3>
                                <div data-scroll class="slide-in">
                                    {% for researcher in researcher_group.researchers %}
                                        {% set researcher = get_post(researcher) %}
                                        {% include 'partials/cards/collaborator-card.twig' with {
                                            link: researcher.link,
                                            classes: '-smaller',
                                            collaborator: researcher
                                        } %}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}

            {% if post.meta('partners') %}
                <div data-scroll class="slider-wrapper -partners slide-in">
                    <h2  class="title wp-block-heading">{{ __('Partenaires', 'laboratoire-vivant') }}</h2>
                    <div class="partners-list grid">
                    {% for partner in post.meta('partners') %}
                        <div data-scroll class="partner-item slide-in col-12 col-t-lg-6">
                            {% include 'partials/cards/partner-card.twig' with {
                                partner: partner
                            } %}
                        </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}

            {% if posts_slides %}
                <div data-scroll class="slider-wrapper -posts slide-in">
                    <h2 class="title wp-block-heading">{{ __('Pour en savoir plus', 'laboratoire-vivant') }}</h2>
                    {% include 'partials/sliders/posts-slider.twig' with {
                        slides: posts_slides
                    } %}
                </div>
            {% endif %}


            {% if action_fields_slides %}
                <div data-scroll class="slider-wrapper -action-fields slide-in">
                    <h2 class="title wp-block-heading">{{ __('Nos champs d\'action', 'laboratoire-vivant') }}</h2>
                    {% include 'partials/sliders/action-fields-slider.twig' with {
                        slides: action_fields_slides
                    } %}
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}