{% extends 'base.twig' %}

{% block mouse_tracker %}
    {% embed 'partials/snippets/mouse-tracker.twig' %}
        {% block content %}
            <div class="mouse-tracker-item">
                <img src="{{ theme.link }}/assets/images/icons/action-fields-mouse.svg" alt="data">
            </div>
        {% endblock %}
    {% endembed %}
{% endblock %}

{% block content %}

{% include 'partials/heroes/actions_fields-hero.twig' with {
    surtitle: __('Champs d’action', 'laboratoire-vivant'),
    title: post.title,
    description: post.content
} %}

<div id="content" class="container -wide gutenberg">
    {% if actions_fields %}
        <div class="actions-fields-section grid" data-module-post-item>
        {% for action_field in actions_fields %}
            {% set reverse = '' %} 
            {% if loop.index is odd %} 
                {% set reverse = '-reverse' %}
            {% endif %}
            <div data-scroll class="actions-fields-section-item slide-in col-12 {% if not loop.last or actions_fields|length is even %}col-t-lg-6 {% else %} full-width{% endif %}" >
            {% include 'partials/cards/actions_fields-card.twig' with {
                action_field: action_field,
                classes: 'jsBlockLink -bigger ' ~ reverse,
                without_link: true
            } %}
            </div>
        {% endfor %}
        </div>
    {% endif %}
</div>

    {% include 'partials/what-lvlc.twig' %}
{% endblock %}