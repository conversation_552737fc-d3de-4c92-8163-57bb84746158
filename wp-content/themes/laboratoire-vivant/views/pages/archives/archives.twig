{% extends 'base.twig' %}
{% block content %}

    {% include 'partials/heroes/title-illustration-hero.twig' with {
        title: post.title,
        illustration: '/assets/images/hero/archives-illustration.svg'
    } %}

    <div class="container archives-container" data-module-filter>
        <div data-module-ajax-content>
            <div data-scroll data-ajax-content="dynamic-content" class="slide-in" id="ajax-content">
                {% include 'partials/loading.twig' %}
                <form id="ajax-form">
                    <input type="hidden" id="ajax-settings" data-url="{{ url }}" data-limit="{{ limit ?: 20 }}">

                    <div class="filters-wrapper">
                        {% include 'partials/filters/filters.twig' with {
                            search: filters.search,
                            filters: filters.filters,
                        } %}
                    </div>

                </form>
                <div id="append-ajax" class="posts-list inner grid">
                    {% include 'partials/lists/posts-list.twig' with { 
                        results: results,
                    } %}
                </div>
                <div id="ajax-pagination">
                {% include 'partials/snippets/load-more.twig' with {
                    text : "Voir plus",
                    disabled: pages_total <= paged ? true : false ,
                } %}
            </div>
        </div>
    </div>
{% endblock %}