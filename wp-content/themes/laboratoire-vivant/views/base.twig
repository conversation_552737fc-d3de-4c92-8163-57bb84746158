<!doctype html>
<!--[if lt IE 9]><html class="no-js no-svg ie lt-ie9 lt-ie8 lt-ie7" {{ site.language_attributes }}> <![endif]-->
<!--[if IE 9]><html class="no-js no-svg ie ie9 lt-ie9 lt-ie8" {{ site.language_attributes }}> <![endif]-->
<!--[if gt IE 9]><!-->
<html class="no-js no-svg" {{ site.language_attributes }}> 
<!--<![endif]-->

<head>
	<meta charset="{{ site.charset }}" />
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="{{ site.theme.link }}/style.css" type="text/css" media="screen" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
	{{ function('wp_head') }}
	<link rel="apple-touch-icon" sizes="180x180" href="{{ theme.link }}/assets/favicon/apple-touch-icon.png">
	<link rel="icon" type="image/png" sizes="96x96" href="{{ theme.link }}/assets/favicon/favicon-96x96.png">
	<link rel="manifest" href="{{ theme.link }}/assets/favicon/site.webmanifest">
	<meta name="msapplication-TileColor" content="#da532c">
	<meta name="theme-color" content="{{ theme_color ? theme_color : '#FFFFFF' }}">
	<link id="app-css" rel="stylesheet" href="{{ theme.uri ~ '/dist/styles/app.css' }}" media="print" onload="this.media='all'; this.onload=null; this.loaded=true">
	<style>{{ source('dist/styles/critical.css') }}</style>

	{# Share #}
	{# <script type="text/javascript" src="//platform-api.sharethis.com/js/sharethis.js#property=#{property?._id}&product=custom-share-buttons"></script> #}
</head>

<body class="{{ body_class }} -no-scroll">
	<div class="preloader">
		{% include 'partials/snippets/svg/logo-preloader.twig' %}
	</div>

	{% block mouse_tracker %} {% endblock %}
	<div id="smooth-wrapper">
		<div id="smooth-content">
			{% if user %}<div class="admin-gap"></div>{% endif %}
			{% include 'partials/snippets/skip-content.twig' %}

			{% include 'partials/commons/header.twig' %}

			<main id="main-content" class="main-content">
				{% block content %} {% endblock %}
			</main>

			{% include 'partials/commons/footer.twig' %}
		</div>
	</div>

	{{ function('wp_footer') }}

</body>
</html>