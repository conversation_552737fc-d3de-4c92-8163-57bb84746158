{% set slides = fields["slides"] ?: block['data']['slides'] %}

{% extends 'partials/commons/skeleton-slider.twig' %}

{% block class %}content-slider{% endblock %}

{% block wrapper_classes %}jsColors{% endblock %}

{% block nav %}
    <button data-swiper="prev" class="prev" title="{{ __('Previous slide', 'laboratoire-vivant') }}"><span class="icon-arrow-simple -left"></span></button>
	<button data-swiper="next" class="next" title="{{ __('Next slide', 'laboratoire-vivant') }}"><span class="icon-arrow-simple -right"></span></button>
{% endblock %}

{% block slides %}
	{% for slide in slides %}
		<div tabindex="0" class="swiper-slide" data-scroll>
            <h2 class="title">{{ slide.title }}</h2>
            <h3 class="subtitle">{{ slide.subtitle }}</h3>
            <div class="text">{{ slide.text }}</div>
		</div>
	{% endfor %}
{% endblock %}

