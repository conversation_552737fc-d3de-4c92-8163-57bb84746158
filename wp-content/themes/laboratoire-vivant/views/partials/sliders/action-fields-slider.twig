{% extends 'partials/commons/skeleton-slider.twig' %}

{% block class %}action-fields-slider{% endblock %}

{% block wrapper_classes %}no-jsColors{% endblock %}

{% block nav %}
    <div class="navigation-ctn">
        <button data-swiper="prev" class="prev" title="{{ __('Previous slide', 'laboratoire-vivant') }}"><span class="icon-arrow-simple -left"></span></button>
	    <button data-swiper="next" class="next" title="{{ __('Next slide', 'laboratoire-vivant') }}"><span class="icon-arrow-simple -right"></span></button>
    </div>
{% endblock %}

{% block slides %}
	{% for slide in slides %}
        {% if loop.index is odd %} 
            {% set classes = 'swiper-slide -reverse' %}
        {% else %}
            {% set classes = 'swiper-slide' %}
        {% endif %}
        {% include 'partials/cards/actions_fields-card.twig' with {
            action_field: slide,
            classes: classes ~ ' jsBlockLink',
            data_scroll: true
        } %}
	{% endfor %}
{% endblock %}