{% extends 'partials/commons/skeleton-slider.twig' %}

{% block class %}action-fields-slider{% endblock %}

{% block wrapper_classes %}no-jsColors{% endblock %}

{% block nav %}
    <button data-swiper="prev" class="prev" title="{{ __('Previous slide', 'laboratoire-vivant') }}"><span class="icon-arrow-simple -left"></span></button>
	<button data-swiper="next" class="next" title="{{ __('Next slide', 'laboratoire-vivant') }}"><span class="icon-arrow-simple -right"></span></button>
{% endblock %}

{% block slides %}
	{% for slide in slides %}
		<div tabindex="0" class="swiper-slide jsBlockLink {{ slide.meta('color') }}{% if loop.index is odd %} -reverse{% endif %}" data-scroll>
            <div class="content">
            {% if slide.meta('illustration') %}
                <div class="illustration">
                    <img src="{{theme.link}}/assets/images/actions-fields/{{ slide.meta('illustration') }}.svg" alt="{{ slide.title }}" />
                </div>
            {% endif %}
                <h2 class="title">{{ slide.title }}</h2>
            </div>
            <div class="link-wrapper">
                <a href="{{ slide.link }}" class="link"><span class="icon-arrow-export"></span></a>
            </div>
		</div>
	{% endfor %}
{% endblock %}