{% extends 'partials/commons/skeleton-slider.twig' %}

{% block class %}team-people-slider{% endblock %}

{% block wrapper_classes %}no-jsColors{% endblock %}

{% block nav %}
    <div class="navigation-ctn">
        <button data-swiper="prev" class="prev" title="{{ __('Previous slide', 'laboratoire-vivant') }}"><span class="icon-arrow-simple -left"></span></button>
	    <button data-swiper="next" class="next" title="{{ __('Next slide', 'laboratoire-vivant') }}"><span class="icon-arrow-simple -right"></span></button>
    </div>
{% endblock %}

{% block slides %}
	{% for slide in slides %}
        {% set slide = get_post(slide) %}
		<div class="swiper-slide" data-scroll>
            {% include 'partials/cards/collaborator-card.twig' with {
                collaborator: slide,
                classes: '-big -always'
            } %}
		</div>
	{% endfor %}
{% endblock %}