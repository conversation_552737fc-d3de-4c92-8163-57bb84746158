{% extends 'partials/commons/skeleton-slider.twig' %}

{% block class %}posts-slider{% endblock %}

{% block wrapper_classes %}no-jsColors{% endblock %}

{% block nav %}
    <div class="navigation-ctn">
        <button data-swiper="prev" class="prev" title="{{ __('Previous slide', 'laboratoire-vivant') }}"><span class="icon-arrow-simple -left"></span></button>
	    <button data-swiper="next" class="next" title="{{ __('Next slide', 'laboratoire-vivant') }}"><span class="icon-arrow-simple -right"></span></button>
    </div>
{% endblock %}

{% block slides %}
	{% for slide in slides %}
        {% set slide = get_post(slide) %}
		<div class="swiper-slide jsBlockLink" data-scroll>
            <div class="header">
                {% include 'partials/posts/meta-post.twig' with {
                    category_color: get_term_fields_color(slide.category.term_id),
                    category: slide.category,
                    date: slide.date
                } %}
                <div class="content">
                    <a href="{{ slide.link }}"><h2 class="title">{{ slide.post_title }}</h2></a>
                </div>
            </div>
            {% include 'partials/posts/authors-post.twig' with {
                authors: slide.meta('authors')
            } %}
		</div>
	{% endfor %}
{% endblock %}