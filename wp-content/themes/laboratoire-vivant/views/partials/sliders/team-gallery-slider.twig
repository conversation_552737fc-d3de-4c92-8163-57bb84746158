{% extends 'partials/commons/skeleton-slider.twig' %}

{% block class %}team-gallery-slider{% endblock %}

{% block wrapper_classes %}no-jsColors{% endblock %}

{% block nav %}
    <div class="navigation-ctn">
        <button data-swiper="prev" class="prev" title="{{ __('Previous slide', 'laboratoire-vivant') }}"><span class="icon-arrow-simple -left"></span></button>
	    <button data-swiper="next" class="next" title="{{ __('Next slide', 'laboratoire-vivant') }}"><span class="icon-arrow-simple -right"></span></button>
    </div>
{% endblock %}

{% block slides %}
	{% for slide in slides %}
		<div class="swiper-slide jsBlockLink {{ slide.meta('color') }}{% if loop.index is odd %} -reverse{% endif %}" data-scroll>
            <div class="content">
                <img src="{{ slide.url }}" alt="{{ slide.alt }}" class="image">
            </div>
		</div>
	{% endfor %}
{% endblock %}