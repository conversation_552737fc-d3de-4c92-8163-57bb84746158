{% for category in categories %}
    {% if category.count > 0 %}
        <div class="category-wrapper">
            <h2 data-scroll class="category-title slide-in -small">{{ category.name }}</h2>
            <div class="team-list grid">
                {% for team in category.posts %}
                    <div class="col-12 col-t-lg-4 team-item{% if loop.index > 6 %} -hide{% endif %}" data-category="{{ category.ID }}">
                    {% include 'partials/cards/collaborator-card.twig' with {
                        collaborator: team,
                        link: team.link,
                        classes: '-big'
                    } %}
                    </div>
                {% endfor %}
            </div>
            {% if category.posts|length > 6 %}
                {% include 'partials/snippets/load-more.twig' with {
                    text : "Voir plus",
                    category: category.ID
                } %}
            {% endif %}
        </div>
    {% endif %}
{% endfor %}