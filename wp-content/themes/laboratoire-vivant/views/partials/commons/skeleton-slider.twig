{# To use It
  {% extends 'partials/common/skeleton-slider.twig' %}

  {% block class %}custom-swiper-class{% endblock %} // optional
  {% block wrapper_classes %}jsColors{% endblock %} // optional - default is jsColors

  {% block title %} <h2>{{ __('Your title', 'kryzalid') }}</h2> {% endblock %} // optional
  {% block nav %}{% endblock %} // optional

  {% block slides %}
    {% for slide in slides %}
      <img src="">
    {% endfor %}
  {% endblock %}
 #}
{% set slider_class = block('class') %}
{% set wrapper_classes = block('wrapper_classes') ?: 'jsColors' %}

<div data-module-swiper>
  <div class="slider {{ slider_class }}" data-swiper="{{ slider_class }}" style="width:100vw;">

    <div class="slider-inner">
      <div class="swiper-slides">

          <div class="swiper-container" data-module-post-item>
              <div class="swiper-wrapper {{ wrapper_classes }}"  data-post-item="item">
                {% block slides %}{% endblock %}
              </div>
              <div class="swiper-pagination"></div>
          </div>

          <div class="swiper-navigation">
              {% if block('nav') %}
                {{ block('nav') }}
              {% else %}
                <a class="prev" href="" title="{{ __('Previous slide', 'kryzalid') }}"></a>
                <a class="next" href="" title="{{ __('Next slide', 'kryzalid') }}"></a>
              {% endif %}
          </div>

      </div>
    </div>
  </div>
</div>