<div class="filters-ctn" data-module-select>

    <div class="filters">

        <div class="select-wrapper grid">
            {% if filters.search is not defined %}
            <div class="col-12 col-t-lg-6">
               {% for filter in filters %}
                    {% if filter.type == 'select' %} 
                        <div class="filter-group">
                            <button type="button" 
                                    class="label toggle" 
                                    data-select="group" 
                                    tabindex="0"
                                    aria-haspopup="true"
                                    aria-expanded="false"
                                    aria-controls="popup-{{ filter.id }}">
                                {{ filter.label }} <i class="icon-plus"></i>
                            </button>
                            {% include 'partials/filters/filter-popup.twig' with {
                                filter: filter
                            } %}
                        </div>
                    {% endif %}
                {% endfor %}
            </div>
            
            <div class="col-12 col-t-lg-6">
               {% for filter in filters %}
                    {% if filter.type == 'radio' %}
                        <fieldset class="filter-radio" role="radiogroup" aria-labelledby="radio-group-{{ filter.id }}">
                            <legend id="radio-group-{{ filter.id }}" class="sr-only">{{ filter.label }}</legend>
                            {% for option in filter.options %}
                                <div class="option">
                                    <input type="radio" 
                                        id="{{ filter.id ~ '-' ~ option.value }}" 
                                        name="{{ filter.id }}" 
                                        value="{{ option.value }}"
                                        tabindex="-1"
                                        data-select="radio"
                                        {% if loop.first and (params[filter.id] is empty or 'all' in params[filter.id]|split(',')) %}
                                            checked
                                        {% elseif option.value in params[filter.id]|split(',') %}
                                            checked
                                        {% endif %}
                                    >
                                    <label class="label toggle" 
                                           for="{{ filter.id ~ '-' ~ option.value }}"
                                           tabindex="0"
                                           role="radio"
                                           aria-checked="{% if loop.first and (params[filter.id] is empty or 'all' in params[filter.id]|split(',')) %}true{% elseif option.value in params[filter.id]|split(',') %}true{% else %}false{% endif %}">
                                        {% if option.icon %}<i class="icon-{{ option.icon }}"></i>{% endif %}
                                        <span>{{ option.label }}</span>
                                    </label>
                                </div>
                            {% endfor %}
                        </fieldset>
                    {% endif %}
                {% endfor %}
            </div>
            {% endif %}

            {% if search %}
                <div class="search-ctn col-12">
                    <label for="search" class="sr-only">{{ search_placeholder }}</label>
                    <input type="text" 
                           name="search" 
                           id="search" 
                           data-select="search" 
                           placeholder="{{ search_placeholder }}" 
                           value=""
                           aria-describedby="search-description">
                    <div id="search-description" class="sr-only">
                        {{ __('Utilisez Entrée pour rechercher ou les flèches pour naviguer dans les résultats', 'laboratoire-vivant') }}
                    </div>
                    <input type="hidden" name="type" value="{{ post_type }}">
                    <button data-select="searchBtn" 
                            class="search-btn"
                            aria-label="{{__('Rechercher','laboratoire-vivant')}}">
                        <i class="icon-search"></i>
                    </button>
                </div>
            {% endif %}
        </div>

    </div>

    <div class="selected-row" 
         data-select="selected-row" 
         role="region" 
         aria-label="{{ __('Filtres sélectionnés', 'laboratoire-vivant') }}"
         {% if selected_filters_count > 0 %}aria-live="polite"{% endif %}>
        {% for filter in filters %}
        {% if not filter.skip %}
            {% for option in filter.options %}
                {% if (option.value in params[filter.id]|split(',')) %}
                    <button data-target="{{ filter.id ~ '-' ~ option.value }}" 
                            tabindex="0" 
                            aria-label="{{__('Retirer le filtre: ' ~ option.label,'laboratoire-vivant')}}" 
                            data-select="uncheck">
                        {{ option.label }}
                        <i class="icon-plus"></i>
                    </button>
                {% endif %}
            {% endfor %}
        {% endif %}
    {% endfor %}
    </div>

</div>
