<div class="title-illustration-hero">
    <div class="container grid {% if team %}team-container{% endif %}">
        <div class="title-wrapper col-12 col-t-lg-6">
            <h1 data-module-mask-title data-duration="2" data-delay="1.4" class="title">{{ title }}</h1>
        </div>

        <div data-scroll class="illustration fade-in col-12 col-t-lg-6">
            <img src="{{ theme.link ~ illustration }}" alt="{{ title }}" class="illustration-image">
        </div>
    </div>
    {% if team %}
        <div data-scroll class="container fade-in team-wrapper">
            <ul class="team-list grid">
                {% set team_config = get_team_config() %}
                {% for post_type, config in team_config %}
                    {% set count_key = 'count_' ~ post_type ~ 's' %}
                    {% if count_key in _context and _context[count_key] > 0 and static_pages[config.page_key] %}
                    <li class="team-item col-6 col-t-lg-3 jsBlockLink {% if current_link == static_pages[config.page_key].link %}active{% endif %}">
                        <a href="{{ static_pages[config.page_key].link }}" class="team-link -h4">
                            {{ __(config.label, 'laboratoire-vivant') }}
                        </a>
                    </li>
                    {% endif %}
                {% endfor %}
            </ul>
        </div>
    {% endif %}
</div>

