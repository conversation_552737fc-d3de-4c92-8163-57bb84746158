import { module } from 'modujs';

import Swiper from 'swiper';
import { Navigation, Autoplay, Pagination, EffectFade} from 'swiper/modules';


Swiper.use([Navigation, Autoplay, Pagination, EffectFade]);

export default class extends module {
    
        constructor(m) {
            super(m);
    
            this.$contentSlider = this.$('content-slider');
            this.$actionFieldsSlider = this.$('action-fields-slider');
        }

        multipleBreakpoints = {
            // 1400: {slidesPerView: 3.2},
            1200: {
                slidesPerView: 2.3,
                spaceBetween: 50
            },
            540: {
                slidesPerView: 1.8,
                spaceBetween: 20
            },
            320: {
                slidesPerView: 1.1,
                spaceBetween: 20
            }
        }

        gutenbergContentBreakpoints = {
            1200: {
                slidesPerView: 2.2,
                spaceBetween: 20
            },
            540: {
                slidesPerView: 1.5,
                spaceBetween: 20
            },
            320: {
                slidesPerView: 1,
                spaceBetween: 20
            }
        }
        
    
        init() {

            // Apply Swiper mechanics to all content slider
            this.$contentSlider.forEach((el, index) => {
                const id = 'slider-' + index;
                el.setAttribute('id', id);
                const swiperContainer = el.querySelector('.swiper-container');
                const slider = new Swiper(swiperContainer, {
                    speed: 300,
                    loop: false,
                    breakpoints: this.gutenbergContentBreakpoints,
                    navigation: { nextEl: `#${id} .next`, prevEl: `#${id} .prev`, }
                });
            });

            this.$actionFieldsSlider.forEach((el, index) => {
                const id = 'slider-' + index;
                el.setAttribute('id', id);
                const swiperContainer = el.querySelector('.swiper-container');
                const slider = new Swiper(swiperContainer, {
                    speed: 300,
                    loop: false,
                    breakpoints: this.gutenbergContentBreakpoints,
                    navigation: { nextEl: `#${id} .next`, prevEl: `#${id} .prev`, }
                });
            });
        }
    
        destroy() {
            // Destroy Swiper instance
            
            this.$contentSlider.forEach((slider) => {
                slider.destroy();
            });
        }
    }