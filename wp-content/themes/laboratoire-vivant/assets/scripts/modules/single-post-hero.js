import { module } from 'modujs';
import { debounce } from '../utils/tickers';

export default class extends module {
    constructor(m) {
        super(m);
        this.thumbnailWrapper = null;
        this.heroElement = null;
        this.resizeHandler = null;
    }

    init() {
        // Vérifier que nous sommes sur une page single-post
        if (!document.body.classList.contains('single-post')) {
            return;
        }

        // Vérifier que l'élément avec has-thumbnail existe
        this.heroElement = document.querySelector('.single-post-hero.has-thumbnail');
        if (!this.heroElement) {
            return;
        }

        // Vérifier que le thumbnail wrapper existe
        this.thumbnailWrapper = document.querySelector('.post-thumbnail-wrapper');
        if (!this.thumbnailWrapper) {
            return;
        }

        // Créer le handler de resize avec debounce
        this.resizeHandler = debounce(() => {
            this.adjustHeroHeight();
        }, 200);

        // Appliquer la hauteur initiale
        this.adjustHeroHeight();

        // Écouter les changements de taille de fenêtre
        window.addEventListener('resize', this.resizeHandler);
    }

    adjustHeroHeight() {
        if (!this.heroElement || !this.thumbnailWrapper) {
            return;
        }

        // Récupérer la hauteur de base du hero (depuis le CSS)
        const baseHeight = 710; // Hauteur définie dans le CSS

        // Récupérer les dimensions du thumbnail wrapper
        const thumbnailRect = this.thumbnailWrapper.getBoundingClientRect();
        const thumbnailHeight = thumbnailRect.height;

        // Calculer 20% de la hauteur du thumbnail
        const additionalHeight = thumbnailHeight * 0.25;

        // Calculer la nouvelle hauteur totale (base + additionnel)
        const newTotalHeight = baseHeight + additionalHeight;

        // Appliquer la nouvelle hauteur
        this.heroElement.style.height = `${newTotalHeight}px`;
    }

    destroy() {
        if (this.resizeHandler) {
            window.removeEventListener('resize', this.resizeHandler);
        }
    }
}
