/**
 * Header Module
 * Gère le menu mobile responsive avec ModuJS
 */

import { module } from 'modujs';
import * as focusTrap from 'focus-trap';

export default class extends module {
    constructor(m) {
        super(m);
        
        this.$mobileMenu = this.$('mobileMenu')[0];
        this.$mobileBtn = this.$('mobileBtn')[0];
        this.$mobileClose = this.$('mobileClose')[0];
        
        this.events = {
            click: {
                mobileBtn: 'toggleMobileMenu',
                mobileClose: 'closeMobileMenu'
            },
            resize: {
                window: 'handleResponsiveBehavior'
            }
        }
    }

    init() {
        this.initFocusTrap();
        this.updateViewportHeightVar();
        this.handleResponsiveBehavior();
        // Mettre à jour --vh sur redimensionnement
        this._boundUpdateVh = this.updateViewportHeightVar.bind(this);
        window.addEventListener('resize', this._boundUpdateVh);
        
        // <PERSON><PERSON><PERSON> pour s'assurer que les styles CSS sont appliqués
        setTimeout(() => {
            this.updateViewportHeightVar();
            this.handleResponsiveBehavior();
        }, 100);
    }

    initFocusTrap() {
        this.trap = focusTrap.createFocusTrap(this.$mobileMenu, {
            allowOutsideClick: true,
            escapeDeactivates: true,
            returnFocusOnDeactivate: true
        });
    }

    // ============================================================================
    // RESPONSIVE BEHAVIOR
    // ============================================================================

    handleResponsiveBehavior() {
        this.updateViewportHeightVar();
        const isMobile = window.innerWidth <= 992;
        
        if (isMobile) {
            this.enableMobileMenu();
        } else {
            this.disableMobileMenu();
        }
    }

    // Met à jour la variable CSS --vh pour gérer correctement la hauteur sur iOS/Android
    updateViewportHeightVar() {
        const vhUnit = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vhUnit}px`);
    }

    enableMobileMenu() {
        if (this.$mobileBtn) {
            this.$mobileBtn.removeAttribute('tabindex');
            this.$mobileBtn.removeAttribute('aria-hidden');
            this.$mobileBtn.removeAttribute('inert');
        }
        
        if (this.$mobileClose) {
            this.$mobileClose.removeAttribute('tabindex');
            this.$mobileClose.removeAttribute('aria-hidden');
            this.$mobileClose.removeAttribute('inert');
        }
        
        // Rendre le menu mobile focusable (mais ne pas gérer aria-hidden)
        if (this.$mobileMenu) {
            this.$mobileMenu.removeAttribute('inert');
        }
    }

    disableMobileMenu() {
        if (this.$mobileBtn) {
            this.$mobileBtn.setAttribute('tabindex', '-1');
            this.$mobileBtn.setAttribute('aria-hidden', 'true');
            this.$mobileBtn.setAttribute('inert', '');
        }
        
        if (this.$mobileClose) {
            this.$mobileClose.setAttribute('tabindex', '-1');
            this.$mobileClose.setAttribute('aria-hidden', 'true');
            this.$mobileClose.setAttribute('inert', '');
        }
        
        // Rendre tout le menu mobile non-focusable (mais ne pas gérer aria-hidden)
        if (this.$mobileMenu) {
            this.$mobileMenu.setAttribute('inert', '');
        }
        
        // Fermer le menu s'il est ouvert
        if (this.$mobileMenu.classList.contains('-open')) {
            this.closeMobileMenu();
        }
    }

    // ============================================================================
    // MENU INTERACTIONS
    // ============================================================================

    toggleMobileMenu() {
        // Empêcher l'ouverture du menu en desktop
        if (window.innerWidth > 992) {
            return;
        }
        
        this.updateViewportHeightVar();
        if (this.$mobileMenu.classList.contains('-open')) {
            this.closeMobileMenu();
        } else {
            this.openMobileMenu();
        }
    }

    openMobileMenu() {
        // S'assurer que le menu est bien activé pour mobile
        this.enableMobileMenu();
        this.updateViewportHeightVar();
        
        this.$mobileBtn.setAttribute('aria-expanded', 'true');
        // Ne pas gérer aria-hidden sur le menu - laisser le focus-trap s'en occuper
        this.$mobileMenu.classList.add('-open');
        document.body.classList.add('-no-scroll');
        document.body.classList.add('mobile-menu-opened');
        
        this.trap.activate();
    }

    closeMobileMenu() {
        // Calculer le délai total basé sur le nombre d'éléments du menu
        const menuItems = this.$mobileMenu.querySelectorAll('.primary-menu li');
        const totalDelay = 0.1 + (menuItems.length * 0.05) + 0.3; // 0.1s + délais éléments + 0.3s logo
        
        // Retirer les classes immédiatement pour déclencher l'animation de fermeture
        document.body.classList.remove('-no-scroll');
        document.body.classList.remove('mobile-menu-opened');
        this.$mobileBtn.setAttribute('aria-expanded', 'false');
        this.$mobileMenu.classList.remove('-open');
        
        // Attendre la fin de l'animation avant de désactiver le focus trap
        setTimeout(() => {
            this.trap.deactivate();
        }, totalDelay * 1000);
    }

    destroy() {
        if (this._boundUpdateVh) {
            window.removeEventListener('resize', this._boundUpdateVh);
        }
        if (this.$mobileMenu.classList.contains('-open')) {
            this.closeMobileMenu();
        }
        
        if (this.trap) {
            this.trap.deactivate();
        }
    }
} 