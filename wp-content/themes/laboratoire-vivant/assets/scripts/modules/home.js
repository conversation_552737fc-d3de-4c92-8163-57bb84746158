import { module } from 'modujs';
import { gsap } from "gsap";
import { DrawSVGPlugin } from "gsap/DrawSVGPlugin";

export default class extends module {

    constructor(m) {
        super(m);

        gsap.registerPlugin(DrawSVGPlugin);

        this.events = {
            click:{
            }
        }
        this.$paths = this.$('paths-bg')[0].querySelectorAll("path");
        this.$storyLines = this.$('storyline');
    }

    init() {
        this.showIllustration();
        this.initOverlapp();
    }

    showIllustration() {
        gsap.fromTo(this.$paths, {
            drawSVG: "0%",
        }, {
            drawSVG: "100%",
            duration: 3,
            stagger: 0.01,
            ease: "power3.Out",
            scrollTrigger: {
                trigger: "svg[data-home='main-illustration']",
                start: "20% 80%",
                toggleActions: "restart none none none",
                // markers: true
            }
        });

        // this.paths.forEach((path, i) => {
        //     gsap.fromTo(path, 
        //         { drawSVG: "0%" }, 
        //         {
        //             drawSVG: "100%",
        //             opacity: 1,
        //             ease: "none",
        //             scrollTrigger: {
        //                 trigger: path,
        //                 start: "top 90%",
        //                 end: "20% center",
        //                 scrub: true,
        //                 // markers: true
        //             }
        //         }
        //     );
        // });
    }

    initOverlapp() {
        this.$storyLines.forEach((card, i) => {
            // Sélectionner les éléments à animer dans chaque carte
            const cardTitle = card.querySelector('h2, h3, .card-title');
            const cardText = card.querySelector('p, .card-text');
            const cardImage = card.querySelector('img, .card-image');
            const cardButton = card.querySelector('button, .btn, .card-button');

            // Créer une timeline spécifique pour chaque card
            const tl = gsap.timeline({
                scrollTrigger: {
                    trigger: card,
                    start: "top top",
                    end: "bottom bottom",
                    endTrigger: this.el,
                    pin: card,
                    // pinSpacing: false,
                    scrub: 1, // Contrôle fluide avec le scroll
                    id: `Card-${i + 1}`,
                    // markers: true,
                }
            });

            // Animation d'entrée de la carte
            tl.fromTo(card,
                {
                    opacity: 0,
                    scale: 0.9,
                    y: 50
                },
                {
                    opacity: 1,
                    scale: 1,
                    y: 0,
                    duration: 0.3,
                    ease: "power2.out"
                }
            );

            // Animation du titre (si présent)
            if (cardTitle) {
                tl.fromTo(cardTitle,
                    {
                        opacity: 0,
                        y: 30,
                        scale: 0.95
                    },
                    {
                        opacity: 1,
                        y: 0,
                        scale: 1,
                        duration: 0.2,
                        ease: "power2.out"
                    },
                    "-=0.1" // Commence légèrement avant la fin de l'animation précédente
                );
            }

            // Animation du texte (si présent)
            if (cardText) {
                tl.fromTo(cardText,
                    {
                        opacity: 0,
                        y: 20
                    },
                    {
                        opacity: 1,
                        y: 0,
                        duration: 0.2,
                        ease: "power2.out"
                    },
                    "-=0.05"
                );
            }

            // Animation de l'image (si présente)
            if (cardImage) {
                tl.fromTo(cardImage,
                    {
                        opacity: 0,
                        scale: 1.1,
                        filter: "blur(5px)"
                    },
                    {
                        opacity: 1,
                        scale: 1,
                        filter: "blur(0px)",
                        duration: 0.3,
                        ease: "power2.out"
                    },
                    "-=0.15"
                );
            }

            // Animation du bouton (si présent)
            if (cardButton) {
                tl.fromTo(cardButton,
                    {
                        opacity: 0,
                        y: 15,
                        scale: 0.9
                    },
                    {
                        opacity: 1,
                        y: 0,
                        scale: 1,
                        duration: 0.15,
                        ease: "back.out(1.7)"
                    },
                    "-=0.05"
                );
            }

            // Animation de sortie (optionnelle)
            tl.to(card,
                {
                    opacity: 0.7,
                    scale: 0.95,
                    duration: 0.2,
                    ease: "power2.in"
                },
                "+=0.1" // Commence après un délai
            );
        });
    }


    destroy() {
    }
}