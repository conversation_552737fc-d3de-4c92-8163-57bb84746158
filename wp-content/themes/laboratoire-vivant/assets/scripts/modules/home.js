import { module } from 'modujs';
import { gsap } from "gsap";
import { DrawSVGPlugin } from "gsap/DrawSVGPlugin";
import { SplitText } from "gsap/SplitText";

export default class extends module {

    constructor(m) {
        super(m);

        gsap.registerPlugin(DrawSVGPlugin, SplitText);

        this.events = {
            click:{
            }
        }
        this.$paths = this.$('paths-bg')[0].querySelectorAll("path");
        this.$storyLines = this.$('storyline');
    }

    init() {
        this.showIllustration();
        this.initOverlapp();
    }

    showIllustration() {
        gsap.fromTo(this.$paths, {
            drawSVG: "0%",
        }, {
            drawSVG: "100%",
            duration: 3,
            stagger: 0.01,
            ease: "power3.Out",
            scrollTrigger: {
                trigger: "svg[data-home='main-illustration']",
                start: "20% 80%",
                toggleActions: "play none none none",
                // markers: true
            }
        });

        // Keep this because is not defined yet 

        // this.paths.forEach((path, i) => {
        //     gsap.fromTo(path, 
        //         { drawSVG: "0%" }, 
        //         {
        //             drawSVG: "100%",
        //             opacity: 1,
        //             ease: "none",
        //             scrollTrigger: {
        //                 trigger: path,
        //                 start: "top 90%",
        //                 end: "20% center",
        //                 scrub: true,
        //                 // markers: true
        //             }
        //         }
        //     );
        // });
    }

    initOverlapp() {
        const masterTimeline = gsap.timeline({
            scrollTrigger: {
                trigger: '.story-time-ctn',
                start: "top top",
                end: "bottom bottom", 
                pin: true,
                pinSpacing: false,
                scrub: true,
                id: "MasterStoryline",
                markers: true,
            }
        });

        this.$storyLines.forEach((card, i) => {

            const cardTitle = card.querySelector('h2');
            const cardImage = card.querySelector('.image');

            const cardTimeline = gsap.timeline();

            // Hide all cards except the first one
            if (i > 0) {
                gsap.set(card, { opacity: 0, y: 50, scale: 0.9 });
            }

            // Enter animation card
            cardTimeline.fromTo(card,
                {
                    opacity: i === 0 ? 1 : 0,
                    scale: i === 0 ? 1 : 0.9,
                    y: i === 0 ? 0 : 50
                },
                {
                    opacity: 1,
                    scale: 1,
                    y: 0,
                    duration: 0.5,
                    ease: "power2.out"
                }
            );

            if (cardTitle) {
                cardTimeline.fromTo(cardTitle,
                    {
                        opacity: i === 0 ? 1 : 0,
                        scale: i === 0 ? 1 : 0.9,
                    },
                    {
                        opacity: 1,
                        scale: 1,
                        duration: 0.4,
                        ease: "power2.out"
                    },
                    "-=0.2"
                );
            }

            if (cardImage) {
                cardTimeline.fromTo(cardImage,
                    {
                        opacity: i === 0 ? 1 : 0,
                        scale: i === 0 ? 1 : 0.9,
                        filter: i === 0 ? "blur(0px)" : "blur(5px)"
                    },
                    {
                        opacity: 1,
                        scale: 1,
                        filter: "blur(0px)",
                        duration: 0.4,
                        ease: "power2.out"
                    },
                    "-=0.2"
                );
            }

            if (i < this.$storyLines.length - 1) {
                cardTimeline.to(card,
                    {
                        opacity: 0,
                        scale: 0.9,
                        y: -50,
                        duration: 0.3,
                        ease: "power2.in"
                    }
                );
            }

            masterTimeline.add(cardTimeline, i === 0 ? 0 : ">");
        });
    }

    destroy() {
    }
}