import { module } from 'modujs';
import { gsap } from "gsap";
import { DrawSVGPlugin } from "gsap/DrawSVGPlugin";
import { SplitText } from "gsap/SplitText";

export default class extends module {

    constructor(m) {
        super(m);

        gsap.registerPlugin(DrawSVGPlugin, SplitText);

        this.events = {
            click:{
            }
        }
        this.$paths = this.$('paths-bg')[0].querySelectorAll("path");
        this.$storyLines = this.$('storyline');
    }

    init() {
        this.showIllustration();
        this.initOverlapp();
    }

    showIllustration() {
        gsap.fromTo(this.$paths, {
            drawSVG: "0%",
        }, {
            drawSVG: "100%",
            duration: 3,
            stagger: 0.01,
            ease: "power3.Out",
            scrollTrigger: {
                trigger: "svg[data-home='main-illustration']",
                start: "20% 80%",
                toggleActions: "restart none none none",
                // markers: true
            }
        });

        // this.paths.forEach((path, i) => {
        //     gsap.fromTo(path, 
        //         { drawSVG: "0%" }, 
        //         {
        //             drawSVG: "100%",
        //             opacity: 1,
        //             ease: "none",
        //             scrollTrigger: {
        //                 trigger: path,
        //                 start: "top 90%",
        //                 end: "20% center",
        //                 scrub: true,
        //                 // markers: true
        //             }
        //         }
        //     );
        // });
    }

    initOverlapp() {
        const masterTimeline = gsap.timeline({
            scrollTrigger: {
                trigger: '.story-time-ctn',
                start: "top top",
                end: "bottom bottom", 
                pin: true,
                pinSpacing: false,
                scrub: true,
                id: "MasterStoryline",
                markers: true,
            }
        });

        this.$storyLines.forEach((card, i) => {
            // Sélectionner les éléments à animer dans chaque carte
            const cardTitle = card.querySelector('h2, h3, .card-title');
            const cardImage = card.querySelector('img, .card-image');

            // Créer une timeline pour cette carte spécifique
            const cardTimeline = gsap.timeline();

            // Masquer toutes les cartes sauf la première au début
            if (i > 0) {
                gsap.set(card, { opacity: 0, y: 50, scale: 0.9 });
            }

            // Animation d'entrée de la carte
            cardTimeline.fromTo(card,
                {
                    opacity: i === 0 ? 1 : 0,
                    scale: i === 0 ? 1 : 0.9,
                    y: i === 0 ? 0 : 50
                },
                {
                    opacity: 1,
                    scale: 1,
                    y: 0,
                    duration: 0.5,
                    ease: "power2.out"
                }
            );

            // Animation du titre avec effet d'écriture (si présent)
            if (cardTitle) {
                // Créer le SplitText pour diviser le texte en caractères
                const splitTitle = new SplitText(cardTitle, { type: "chars" });

                // Masquer tous les caractères au début
                gsap.set(splitTitle.chars, {
                    opacity: 0,
                    y: 20,
                    rotationX: -90
                });

                // Animation d'écriture caractère par caractère
                cardTimeline.to(splitTitle.chars, {
                    opacity: 1,
                    y: 0,
                    rotationX: 0,
                    duration: 0.05, // Durée par caractère
                    stagger: 0.03, // Délai entre chaque caractère
                    ease: "back.out(1.7)"
                }, "-=0.1");
            }

            // Animation de l'image (si présente)
            if (cardImage) {
                cardTimeline.fromTo(cardImage,
                    {
                        opacity: i === 0 ? 1 : 0,
                        scale: 1.1,
                        filter: "blur(5px)"
                    },
                    {
                        opacity: 1,
                        scale: 1,
                        filter: "blur(0px)",
                        duration: 0.4,
                        ease: "power2.out"
                    },
                    "-=0.2"
                );
            }

            // Pause pour laisser le temps de lire le contenu
            cardTimeline.to({}, { duration: 1 });

            // Animation de sortie (sauf pour la dernière carte)
            if (i < this.$storyLines.length - 1) {
                cardTimeline.to(card,
                    {
                        opacity: 0,
                        scale: 0.9,
                        y: -50,
                        duration: 0.3,
                        ease: "power2.in"
                    }
                );
            }

            // Ajouter cette timeline de carte à la timeline maître
            masterTimeline.add(cardTimeline, i === 0 ? 0 : ">");
        });
    }


    destroy() {
    }
}