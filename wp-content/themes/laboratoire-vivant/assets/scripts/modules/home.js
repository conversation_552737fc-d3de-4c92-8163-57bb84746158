import { module } from 'modujs';
import { gsap } from "gsap";
import { DrawSVGPlugin } from "gsap/DrawSVGPlugin";
import { SplitText } from "gsap/SplitText";
import { ScrollTrigger } from "gsap/ScrollTrigger";

export default class extends module {

    constructor(m) {
        super(m);

        gsap.registerPlugin(DrawSVGPlugin, SplitText, ScrollTrigger);

        this.events = {
            click:{
            }
        }
        this.$storyLines = this.$('storyline');
    }

    init() {
        this.initOverlapp();
    }

    initOverlapp() {

        ScrollTrigger.create({
            trigger: '.story-time-ctn',
            start: "top top",
            end: "top top", 
            endTrigger: '.white-container',
            pin: true,
            pinSpacing: false,
            scrub: true,
            id: "MainPin",
            // markers: true,
        });

        // Timeline animation 
        const masterTimeline = gsap.timeline({
            scrollTrigger: {
                trigger: '.story-time-ctn',
                start: "top 97%",
                end: "bottom bottom", 
                scrub: true,
                id: "MasterStoryline",
                markers: true,
            }
        });

        this.$storyLines.forEach((card, i) => {

            const cardTitle = card.querySelector('h2');
            const cardImage = card.querySelector('.image');
            const cardTimeline = gsap.timeline();

            gsap.set(card, { opacity: 0, y: 50, scale: 0.9 });

            // Enter animation card
            cardTimeline.fromTo(card,
                {
                    opacity: 0,
                    scale: 0.9,
                    y: 50
                },
                {
                    opacity: 1,
                    scale: 1,
                    y: 0,
                    duration: 0.5,
                    ease: "power2.out"
                }
            );

            if (cardTitle) {
                cardTimeline.fromTo(cardTitle,
                    {
                        opacity: 0,
                        scale: 0.9,
                    },
                    {
                        opacity: 1,
                        scale: 1,
                        duration: 0.4,
                        ease: "power2.out"
                    },
                    "-=0.2"
                );
            }

            if (cardImage) {
                cardTimeline.fromTo(cardImage,
                    {
                        opacity: 0,
                        scale: 0.9,
                        filter: "blur(5px)"
                    },
                    {
                        opacity: 1,
                        scale: 1,
                        filter: "blur(0px)",
                        duration: 0.4,
                        ease: "power2.out"
                    },
                    "-=0.2"
                );
            }

            // Not hide last card
            if (i < this.$storyLines.length - 1) {
                cardTimeline.to(card,
                    {
                        opacity: 0,
                        scale: 0.9,
                        y: -50,
                        duration: 0.3,
                        ease: "power2.in"
                    }
                );
            }

            // First card is visible at the start
            if (i === 0) {
                masterTimeline.add(cardTimeline, 0);
            } else {
                masterTimeline.add(cardTimeline, ">");
            }
        });
    }

    destroy() {
    }
}