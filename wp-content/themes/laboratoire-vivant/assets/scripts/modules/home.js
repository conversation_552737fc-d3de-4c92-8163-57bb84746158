import { module } from 'modujs';
import { gsap } from "gsap";
import { DrawSVGPlugin } from "gsap/DrawSVGPlugin";

export default class extends module {

    constructor(m) {
        super(m);

        gsap.registerPlugin(DrawSVGPlugin);

        this.events = {
            click:{
            }
        }
        this.$paths = this.$('paths-bg')[0].querySelectorAll("path");
        this.$storyLines = this.$('storyline');
    }

    init() {
        this.showIllustration();
        this.initOverlapp();
    }

    showIllustration() {
        gsap.fromTo(this.$paths, {
            drawSVG: "0%",
        }, {
            drawSVG: "100%",
            duration: 3,
            stagger: 0.01,
            ease: "power3.out",
            scrollTrigger: {
                trigger: "svg[data-home='main-illustration']",
                start: "20% 80%",
                toggleActions: "play none none none",
                onLeave: () => {
                    gsap.set(this.$paths, { drawSVG: "0%" });
                },
                onEnter: () => {
                    gsap.to(this.$paths, {
                        drawSVG: "100%",
                        duration: 3,
                        stagger: 0.01,
                        ease: "power3.out",
                    });
                }
            }
        });
        


        // this.paths.forEach((path, i) => {
        //     gsap.fromTo(path, 
        //         { drawSVG: "0%" }, 
        //         {
        //             drawSVG: "100%",
        //             opacity: 1,
        //             ease: "none",
        //             scrollTrigger: {
        //                 trigger: path,
        //                 start: "top 90%",
        //                 end: "20% center",
        //                 scrub: true,
        //                 // markers: true
        //             }
        //         }
        //     );
        // });
    }

    initOverlapp() {
        this.$storyLines.forEach((card, i) => {
            // Créer une timeline spécifique pour chaque card
            const tl = gsap.timeline({
                scrollTrigger: {
                    trigger: card,
                    start: "top top",
                    end: "bottom bottom",
                    endTrigger: this.el,
                    pin: card,
                    pinSpacing: false,
                    id: `Card-${i + 1}`,
                    // markers: true,
                }
            });
        });
    }


    destroy() {
    }
}