import { module } from 'modujs';
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import smooth from "../utils/scrollSmoother";

gsap.registerPlugin(ScrollTrigger);

export default class extends module {
    constructor(m) {
        super(m);

        // DOM elements
        this.$element = this.el.querySelector('.anchors-ctn');
        this.$anchors = this.el.querySelectorAll('.anchor');
        
        // State management
        this.sections = [];
        this.scrollTriggers = [];
        this.stickyTrigger = null;
        
        // Bind methods
        this.handleResize = this.handleResize.bind(this);
    }
    
    init() {
        this.setupStickyNavigation();
        window.addEventListener('resize', this.handleResize);
    }

    /**
     * Setup sticky navigation and section detection
     */
    setupStickyNavigation() {
        this.cleanup();
        
        // Only enable on desktop
        if (window.innerWidth >= 991) {
            this.createStickyTrigger();
            this.initSectionDetection();
        }
    }

    /**
     * Create sticky trigger for navigation container
     */
    createStickyTrigger() {
        this.stickyTrigger = ScrollTrigger.create({
            trigger: this.el,
            start: "top 100px",
            endTrigger: ".gutenberg-main-content",
            end: () => `bottom ${this.el.offsetHeight + 100}px`,
            pin: true,
            pinSpacing: false,
            anticipatePin: 1,
        });
    }

    /**
     * Initialize section detection and anchor click handlers
     */
    initSectionDetection() {
        this.$anchors.forEach(anchor => {
            const targetId = this.getTargetId(anchor);
            const section = document.getElementById(targetId);
            
            if (section) {
                this.sections.push({ anchor, section, id: targetId });
                this.bindAnchorClick(anchor, section, targetId);
            }
        });

        this.createSectionTriggers();
    }

    /**
     * Extract target ID from anchor href
     */
    getTargetId(anchor) {
        const href = anchor.getAttribute('href');
        return href?.startsWith('#') ? href.substring(1) : null;
    }

    /**
     * Bind click handler to anchor (prevents duplicates on resize)
     */
    bindAnchorClick(anchor, section, targetId) {
        if (anchor.dataset.anchorBound) return;
        
        anchor.addEventListener('click', (event) => {
            event.preventDefault();
            this.handleAnchorClick(anchor, section, targetId);
        });
        
        anchor.dataset.anchorBound = '1';
    }

    /**
     * Handle anchor click with immediate UI feedback
     */
    handleAnchorClick(anchor, section, targetId) {
        this.setCurrentAnchor(anchor);
        this.updateURLHash(targetId);
        this.scrollToSection(section);
        
        // Ensure current state after scroll completes
        setTimeout(() => this.setCurrentAnchor(anchor), 600);
    }

    /**
     * Update URL hash without page jump
     */
    updateURLHash(targetId) {
        if (history?.replaceState) {
            history.replaceState(null, '', `#${targetId}`);
        }
    }

    /**
     * Create ScrollTrigger for each section to track active state
     */
    createSectionTriggers() {
        this.sections.forEach(({ anchor, section }) => {
            const trigger = ScrollTrigger.create({
                trigger: section,
                start: "top 20%",
                end: "top 20%",
                onEnter: () => this.setCurrentAnchor(anchor),
                onEnterBack: () => this.setCurrentAnchor(anchor)
            });
            
            this.scrollTriggers.push(trigger);
        });
    }

    /**
     * Update current anchor in navigation
     */
    setCurrentAnchor(activeAnchor) {
        this.$anchors.forEach(anchor => anchor.classList.remove('current'));
        activeAnchor?.classList.add('current');
    }

    /**
     * Smooth scroll to target section with proper offset
     */
    scrollToSection(section) {
        const headerOffset = 100;
        const triggerOffset = window.innerHeight * 0.10;
        const targetY = section.getBoundingClientRect().top + window.scrollY - headerOffset - triggerOffset;

        if (smooth?.isSmooth) {
            smooth.scrollTo(targetY);
        } else {
            window.scrollTo({
                top: targetY,
                behavior: 'smooth'
            });
        }
    }

    /**
     * Handle window resize
     */
    handleResize() {
        this.setupStickyNavigation();
    }

    /**
     * Cleanup all triggers and state
     */
    cleanup() {
        this.stickyTrigger?.kill();
        this.scrollTriggers.forEach(trigger => trigger.kill());
        this.scrollTriggers = [];
        this.sections = [];
    }

    /**
     * Destroy instance (ModularJS hook)
     */
    destroy() {
        this.cleanup();
        window.removeEventListener('resize', this.handleResize);
    }
}