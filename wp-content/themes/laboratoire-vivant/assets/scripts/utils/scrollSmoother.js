import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { ScrollSmoother } from "gsap/ScrollSmoother";

// Register GSAP plugins
gsap.registerPlugin(<PERSON>rollTrigger, ScrollSmoother);

class Smooth {
    // Property to store smooth scroll instance
    scroll;

    // Property to store smooth scroll availability
    isSmooth;

    constructor() {
        // Initialize scroll appearance
        document.addEventListener('app:loaded', () => {
            this.initScrollAppear();
        });

        // Check if it's not a touch device
        if (ScrollTrigger.isTouch !== 1) {
            // Create a smooth scroll instance
            this.scroll = ScrollSmoother.create({
                smooth: 1, // Smoothness factor
                effects: true, // Enable effects
            });

            this.isSmooth = true; // Smooth scroll available
        } else {
            this.isSmooth = false; // Smooth scroll not available
            this.scroll = null; // No smooth scroll instance
        }

        const pageHeroScrollDown = document.getElementById('page-hero-scroll-down');
        if (pageHeroScrollDown) {
            pageHeroScrollDown.addEventListener('click', (event) => {
                event.preventDefault();
                const scrollReduce = pageHeroScrollDown.getAttribute('data-scroll-reduce') || 0;
                this.scrollTo('#content');
            });
        }
    }

    /**
     * Initializes scroll-based animations for elements identified by the `data-scroll` attribute.
     * For each element, a ScrollTrigger is created based on the optional data attributes:
     * 
     * - `data-scroll-class`: The class to toggle during the scroll event (default: `-show`).
     * - `data-scroll-trigger`: A selector for a custom trigger element (default: the element itself).
     * - `data-scroll-start-position`: The start position for the scroll reveal (default: 'top 80%').
     * - `data-scroll-end-position`: The end position for the scroll reveal (default: 'bottom top').
     * - `data-scroll-trigger-end`: A selector for a custom end trigger (default: the element itself).
     * - `data-scroll-repeat`: If present, toggles the class on each scroll event; otherwise, adds it once.
     * - `data-scroll-progress`: If present, tracks scroll progress and updates the CSS variable `--scroll-progress`.
     * - `data-scroll-markers`: If present, displays ScrollTrigger markers for debugging.
     * 
     * @returns {void}
     */

    initScrollAppear() {
        document.querySelectorAll('[data-scroll]').forEach((element) => {

            const scrollClass = element.getAttribute('data-scroll-class') || '-show';
            const scrollTrigger = element.getAttribute('data-scroll-trigger') || '';
            const scrollStartPosition = element.getAttribute('data-scroll-start-position') || 'top 80%';
            const scrollEndPosition = element.getAttribute('data-scroll-end-position') || 'bottom top';
            const scrollTriggerEnd = element.getAttribute('data-scroll-trigger-end') || '';
            const hasScrollRepeat = element.hasAttribute('data-scroll-repeat');
            const hasScrollProgress = element.hasAttribute('data-scroll-progress');
            const markers = element.hasAttribute('data-scroll-markers');


            ScrollTrigger.create({
                trigger: scrollTrigger ? document.querySelector(scrollTrigger) : element,
                start: `${scrollStartPosition}`,
                endTrigger: scrollTriggerEnd ? document.querySelector(scrollTriggerEnd) : element,
                end: `${scrollEndPosition}`,
                markers,
                onToggle: ({ isActive }) => {
                    if (hasScrollRepeat)
                        element.classList.toggle(scrollClass, isActive);
                    else if (isActive)
                        element.classList.add(scrollClass);
                },
                onUpdate: ({ progress }) => {
                    if (hasScrollProgress) {
                        element.style.setProperty('--scroll-progress', progress);
                    }
                }
            });
        });
    }

    // Method to scroll to a target
    scrollTo(target, callback = null) {
        const isString = typeof target === 'string';
        const scrollTarget = isString ? document.querySelector(target)?.offsetTop || 0 : target;
    
        if (this.isSmooth) {
            this.scroll.scrollTo(scrollTarget, true);
            if (callback) gsap.to(this.scroll, { onComplete: callback });
        } else {
            if (isString) {
                document.querySelector(target)?.scrollIntoView({ behavior: "smooth", block: "start", inline: "nearest" });
            } else {
                window.scrollTo({ top: target, behavior: "smooth" });
            }
    
            if (callback) setTimeout(() => callback?.(), 600);
        }
    }
}

// Create an instance of Smooth class
const smooth = new Smooth();
export default smooth;