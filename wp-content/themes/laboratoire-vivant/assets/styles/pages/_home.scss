/***
** CSS style on page must be only for twick some litle detail
** Please keep modular structure with partials and component
****/


// Just sample testing code -> Delete it 
body.home{
    --header-color: #{$lv-beige-bg};
    --color-bg: #{$lv-beige-bg};

    .white-container{
        position: relative;
        z-index: 999;
        padding-top: rs(120px, 175px);
        border-radius: rs(40px, 64px);
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
        background-color: white;
    }

    footer {
        border-top-left-radius: 0;
        border-top-right-radius: 0;
    }
}
