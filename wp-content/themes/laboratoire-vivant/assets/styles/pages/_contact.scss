body.page-template-contact {
    --header-color: #{$lv-beige-bg};
    --color-bg: #{$lv-beige-bg};

    .main-content {
        padding-top: rs(100px, 130px);
        padding-bottom: rs(80px, 110px);

        .title {
            margin: 0;
        }

        .grid {
            margin-bottom: rs(80px, 130px);
        }

        .text-column {
            padding-right: grid-space(math.div(1,12), 1);

            @media (max-width: $tablet-lg) {
                padding-right: 0;
                margin-bottom: rem(75px);
            }

            .content {

                &.group-1 {
                    margin: rem(72px) 0;
                }

                .title {
                    margin: 0;
                    margin-bottom: rem(40px);
                }

                .content {
                    margin: 0;
                }
            }
        }

        .gform_wrapper.gform-theme {
            background-color: $white;
            padding: rem(35px) rem(50px);
            border-radius: rem(32px);

            @media (max-width: $tablet-lg) {
                padding: rem(64px) rem(24px);
            }

            .gform_fields {
                display: flex;
                flex-wrap: wrap;
                gap: rem(20px);

                .gfield:not(.gfield--type-captcha) {
                    width: 100%;
                    border-bottom: 1px solid rgba($black, 0.50);
                    
                    &.-half {
                        width: calc(50% - rem(10px));

                        @media (max-width: $tablet-lg) {
                            width: 100%;
                        }
                    }

                    input,
                    textarea {
                        border: none;
                        background: none;
                        box-shadow: none;
                        padding: rem(24px) 0;
                        height: rem(56px);
                        @extend .--small;
                    }
                }
            }

            .gform_footer {
                margin-top: rem(40px);

                input.gform_button {
                    margin-left: auto;
                    @extend .-primary;
                    color: $black;
                }
            }
        }
    }

    .contact-illustration {
        width: 100%;
    }
}
