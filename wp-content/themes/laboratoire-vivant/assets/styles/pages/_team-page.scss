body.page-template-team {
    --header-color: #{$lv-vert-bg};
    --color-bg: #{$white};

    .header-wrapper {
        margin: rs(32px, 85px) 0;
        width: 100%;
        align-items: center;

        .filters-wrapper {

            .filters-ctn {
                margin-bottom: 0;
            }
        }

        .selected-row {
            display: none;
        }
    }

    .category-wrapper {

        .category-title {
            margin-top: rs(80px, 120px, $tablet-lg);
            margin-bottom: rs(50px, 60px, $tablet-lg);
        }

        &:first-child {
            .category-title {
                margin-top: 0;
            }
        }

        .team-list {
            gap: 0;

            .-hide {
                display: none;
            }

            // Alternative: cibler directement les collaborateurs dans les colonnes
            .col-12.col-t-lg-4:first-child .collaborator {
                border: 1px solid rgba($black, 0.4);
            }

            .col-12.col-t-lg-4:not(:first-child) .collaborator {
                border: 1px solid rgba($black, 0.4);
                border-left: none;
            }

            // Si il n'y a qu'une seule colonne, garder la bordure complète
            .col-12.col-t-lg-4:only-child .collaborator {
                border: 1px solid rgba($black, 0.4);
            }

            @media (max-width: $tablet-lg) {
                .col-12.col-t-lg-4:first-child .collaborator {
                    border: none;
                    border-top: 1px solid rgba($black, 0.4);
                }

                .col-12.col-t-lg-4:not(:first-child) .collaborator {
                    border: none;
                    border-top: 1px solid rgba($black, 0.4);
                }

                .col-12.col-t-lg-4:only-child .collaborator {
                    border: none;
                    border-top: 1px solid rgba($black, 0.4);
                }
            }
        }        
    }
}