body.login {
    position: relative;
    background-color: $lv-bleu-bg;

    h1 a {
        background-image: none, url("../../assets/images/logos/logo-LVLC-complet.svg");
        background-size: contain;
        height: 180px;
        width: 100%;
        margin-bottom: 40px;

        &:focus {
            box-shadow: none;
            border: none;
        }
    }

    #backtoblog a,
    #nav a,
    .privacy-policy-link{
        font-size: 16px;
        transition: all 0.5s ease;
        &:hover {
            color: $black;
        }
    }

    .privacy-policy-link{
        &:hover {
            color: $black !important;
        }
    }

    #login {
        width: 90%;
        max-width: 500px;

        #login_error {
            border-left-color: $alert;
            color: $black;
        }

        .message {
            border-left-color: $primary;
            color: $black;
            font-family: var(--font-primary);
        }
    }

    #nav{
        font-weight: 300;
    }

    #backtoblog{
        display: none;
    }

    #loginform,
    #lostpasswordform {
        border: 0;
        box-shadow: none;
        background: $lv-bleu-bg;

        label {
            font-size: 14px;
            line-height: 24px;
            font-family: var(--font-primary) !important;
            font-weight: 500;
            color: $black;
            margin-bottom: 10px;
        }

        .button.wp-hide-pw{
            height: 50px;
        }

        input {
            border: 0;
            border: solid 1px rgba($color: #000000, $alpha: .5);
            border-radius: 0;
            font-size: 15px;
            padding: 0 20px;


            &[type="text"], &[type="password"]{
                background-color: $white;
                font-family: var(--font-primary);
                border-radius: 8px;
                height: 50px;
            }

            &[type=checkbox]{
                border: solid 1px $dark-grey;
                background-color: $light-grey;
            }

            &:focus {
                outline: none;
                box-shadow: none
            }
        }

        .button.wp-hide-pw .dashicons {
            color: $black;
        }

        .submit {
            .button {
                background-color: $lv-jaune-accent;
                color: $black;
                font-family: var(--font-primary);
                border: solid 2px $lv-jaune-accent;
                border-radius: 100px;
                font-size: 14px;
                font-weight: 700;
                transition: all 0.5s ease;
                border-radius: 16px;
                transition: all 0.5s ease;

                &:hover {
                    transform: scale(1.05);
                }
            }
        }
    }

    #nav .wp-login-lost-password { 
        color: $black; 
        &:hover { color: $light-grey; }
    }


}