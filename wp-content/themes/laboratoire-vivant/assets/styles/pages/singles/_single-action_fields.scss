body.single-action_fields {

    .gutenberg-main-content {
        padding-top: rem(140px);
        position: relative;

        @media (max-width: $tablet-lg) {
            padding-top: rem(55px);
        }

        .navigation-ctn {
        
            #anchors-container {
                border-left: 1px solid rgba(#231F20, 0.2);

                @media (max-width: $tablet-lg) {
                    border-left: none;
                    border-bottom: 1px solid rgba(#231F20, 0.2);
                    padding-bottom: rem(55px);
                    margin-bottom: rem(55px);
                }

                .anchor {
                    color:#231F20;
                    opacity: 0.7;
                    font-size: rem(17px);
                    font-weight: 500;
                    line-height: 130%;
                    letter-spacing: -0.255px;
                    padding-left: rem(40px);
                    min-height: rem(50px);
                    position: relative;
                    display: flex;
                    align-items: center;
                    margin-bottom: rem(12px);

                    @media (max-width: $tablet-lg) {
                        padding-left: 0;
                        margin-bottom: rem(20px);
                        min-height: auto;
                    }

                    &:before {
                        content: '';
                        display: block;
                        width: 0;
                        height: 100%;
                        background: #6CC7DA;
                        position: absolute;
                        left: 0;
                        top: 0;
                        transition: width 0.3s ease-in-out;
                    }

                    &.current, &:hover {
                        opacity: 1;
                        font-weight: 700;

                        &:before {
                            width: rem(7px);
                        }
                    }
                }
            }
        }

        #gutenberg-content {
            padding-left: grid-space(math.div(1,12), 1);

            &.-full {
                padding-left: 0;
            }

            @media (max-width: $tablet-lg) {
                padding-left: 0;
            }

            > :first-child {
                margin-top: 0;
            }
        }
    }

    .secondary-content {
        --section-bg-color: #{$lv-beige-bg};
        margin-top: rs(150px, 200px);
        background-color: var(--section-bg-color);
        padding: rs(80px, 160px) 0;
        
        .researchers-ctn {
            .title {
                margin-bottom: rs(80px, 105px);
            }

            .researchers-list {
                margin-bottom: rs(100px, 160px);

                .researcher-item {
                    &.-primary {
                        padding-right: grid-space(math.div(1,12), 1);

                        @media (max-width: $tablet-lg) {
                            padding-right: 0;
                            margin-bottom: rem(80px);
                        }
                    }

                    &.-secondary {
                        .collaborator {
                            display: flex;
                            align-items: center;

                            .collaborator-image {
                                margin-bottom: 0;
                                margin-right: rem(24px);
                            }
                        }
                    }
                    
                    .researcher-title {
                        margin-top: rem(80px);
                        margin-bottom: rs(30px, 40px);
                        
                        &:first-child {
                            margin-top: 0;
                        }
                    }

                    .collaborator {
                        margin-bottom: rem(20px);

                        &:last-child {
                            margin-bottom: 0;
                        }
                    }
                }
            }
        }

        .partners-list {
            margin-bottom: rs(160px, 230px);
        }

        .slider-wrapper {
            &.-posts {
                margin-bottom: rs(150px, 200px);
            }
        }

        .authors-post .author .author-image {
            border-color: var(--section-bg-color);
        }
    }
}