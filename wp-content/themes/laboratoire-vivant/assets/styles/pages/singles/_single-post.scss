body.single-post {
    --header-color: #{$lv-bleu-bg};

    .post-thumbnail-wrapper {
        width: 90%;
        height: 100%;
        margin: 0 auto;
        margin-top: -25%;
        position: relative;
        z-index: 1;

        .post-thumbnail {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 16px;
        }
    }

    .single-post-content {
        padding-top: 100px;

        .excerpt {
            @extend .--large;
        }

        .meta-content {
            margin-top: 48px;
            padding-top: 48px;
            margin-bottom: 100px;
            border-top: 1px solid rgba($black, 0.3);
            display: flex;
            align-items: center;
            justify-content: space-between;

            @media (max-width: $tablet) {
                flex-direction: column;
                align-items: flex-start;
                gap: 24px;
            }

            .meta-date {
                color: #333;
                font-size: 14px;
                font-weight: 400;
                line-height: 150%;
                letter-spacing: -0.154px;
            }
        }

        .content {

            p {
                color: rgba(21, 21, 21, 0.70);
            }
        }
    }

    .documents {
        width: 100%;
        margin: 160px 0;

        .document {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            background-color: $white;
            padding: 40px 24px;
            margin: 0;
            transition: all 0.3s ease;

            &:first-child:last-child {
                border-top: 1px solid $black;
                border-bottom: 1px solid $black;
            }

            &:not(:first-child:last-child) {
                border-bottom: 1px solid $black;

                &:first-child {
                    border-top: 1px solid $black;
                }
            }

            .first-part {
                display: flex;
                align-items: center;
                gap: 32px;
            }

            .icon-arrow-link-download {
                background-color: $lv-jaune-accent;
                width: 56px;
                height: 56px;
                border-radius: 100px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: $black;
                font-size: 26px;
                transition: all 0.3s ease;
                flex-shrink: 0;
            }

            &:hover {
                background-color: $lv-beige-bg;

                .icon-arrow-link-download {
                    transform: scale(1.1);
                }
            }
        }
    }

    .authors-list {
        margin-top: rs(120px, 160px);
        padding-top: 160px;
        padding-bottom: 160px;
        background-color: $lv-bleu-bg;

        .main-authors {
            margin-bottom: 120px;

            .title {
                margin-top: 0;
                margin-bottom: 80px;
            }

            .authors-list-content {

                .author {
                    width: 100%;
                    background-color: $white;
                    padding: 64px;
                    border-radius: 40px;
                    display: flex;
                    align-items: flex-start;
                    margin-top: 40px;

                    &:first-child {
                        margin-top: 0;
                    }

                    @media (max-width: $tablet-lg) {
                        flex-direction: column;
                        padding: 24px 24px 40px;
                    }

                    .author-image {
                        width: 185px;
                        height: 185px;
                        border-radius: 100px;
                        overflow: hidden;
                        margin-right: 56px;

                        @media (max-width: $tablet-lg) {
                            margin-right: 0;
                            margin-bottom: 24px;
                            width: 120px;
                            height: 120px;
                        }

                        img {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                        }
                    }

                    .author-content {
                        display: flex;
                        align-items: flex-start;
                        width: calc(100% - 185px);

                        @media (max-width: $tablet-lg) {
                            width: 100%;
                            flex-direction: column;
                        }

                        .author-main {
                            width: 70%;

                            @media (max-width: $tablet-lg) {
                                margin-bottom: 32px;
                                width: 100%;
                            }

                            .author-name {
                                margin: 0;
                                @extend .-h4;
                            }

                            .author-description {
                                margin: 24px 0;
                                @extend .--small;
                            }

                            .author-link {
                                @extend .-cta-large;
                                font-weight: 600;
                                display: flex;
                                align-items: center;
                                gap: 16px;
                                transition: all 0.3s ease;

                                &:hover {
                                    transform: translateX(10px);
                                }
                            }
                        }

                        .author-links {
                            width: 80%;
                            padding-left: 60px;
                            margin-left: 60px;
                            border-left: 1px solid rgba($black, 0.3);

                            @media (max-width: $tablet-lg) {
                                width: 100%;
                                border-left: none;
                                border-top: 1px solid $black;
                                padding-left: 0;
                                padding-top: 40px;
                                margin-left: 0;
                            }

                            .author-links-title {
                                margin-top: 0;
                                margin-bottom: 40px;
                                @extend .--small;
                                color: rgba(21, 21, 21, 0.70);
                            }

                            .author-links-list {
                                display: flex;
                                flex-direction: column;
                                gap: 24px;

                                .author-link {
                                    @extend .-cta-large;
                                    font-weight: 600;
                                    display: flex;
                                    align-items: center;
                                    gap: 16px;
                                    transition: all 0.3s ease;

                                    &:hover {
                                        transform: translateX(10px);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        .other-authors {

            .title {
                margin-top: 0;
                margin-bottom: 80px;

                @media (max-width: $tablet-lg) {
                    font-size: var(--font-size-h3);
                    line-height: var(--line-height-h3);
                }
            }

            .other-authors-list {
                display: flex;
                flex-wrap: wrap;
                gap: 20px;

                @media (max-width: $tablet-lg) {
                    flex-direction: column;
                    gap: 0;
                }

                .collaborator {
                    width: calc(33.33% - 13.33px);
                    border-left: 1px solid rgba(0, 0, 0, 0.4);

                    @media (max-width: $tablet-lg) {
                        width: 100%;
                    }
                }
            }
        }
    }
}