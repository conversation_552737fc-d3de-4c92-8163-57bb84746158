body.single-researcher,
body.single-farm,
body.single-coordinator,
body.single-collaborator {
    --header-color: #{$lv-vert-bg};
    --background: #{$lv-vert-bg};

    .related-posts-section {
        background: var(--background);
        padding: rs(120px, 80px) 0;

        .related-posts-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: rem(64px);

            .title {
                margin: 0;
            }

            .icon {
                @media (max-width: $tablet-lg) {
                    display: none;
                }
            }
        }

        .related-posts {

            .grid {
                gap: 0;
            }
        }

        .article {
            background: $white;
            border-top: none;
            border-left: none;
        }

        .link-more {
            border-radius: 100px;
            background: $white;
            @extend .-h4;
            padding: 24px 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: fit-content;
            gap: 16px;
            margin: 0 auto;
            margin-top: rs(64px, 115px);
            cursor: pointer;
            transition: transform 0.3s ease;

            .icon-plus{
                font-size: 32px;
            }

            &::before{
                animation: none;
            }

            &:disabled{
                opacity: 0.4;
                pointer-events: none;
            }

            &:hover{
                transform: scale(1.05);
            }

            &:disabled{
                transform: none;
                cursor: not-allowed;
            }
        }
    }

    .gallery-section {
        background: var(--background);
        padding: rs(120px, 80px) 0;

        .gallery-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: rem(64px);

            .title {
                margin: 0;
            }

            .icon {
                @media (max-width: $tablet-lg) {
                    display: none;
                }
            }
        }
    }

    .related-people-section {
        padding: rs(120px, 80px) 0;

        .related-people-header {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            width: 60%;
            margin-bottom: rem(64px);
            
            @media (max-width: $tablet-lg) {
                width: 100%;
            }
        }
    }
}