body {
    font-family: var(--font-primary);
    font-size: var(--font-size-body);
    color: var(--color-text);
    background-color: var(--color-bg);
    line-height: var(--line-height-body);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;

    &.-no-scroll{
        margin: 0;
        height: 100%;
        overflow: hidden;
    }

    &.mobile-menu-opened{
        #main-content{
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }
    }
}

.container{
    @include container;

    &.-narrow {
        margin-left: grid-space(math.div(2,12), 1);
        margin-right: grid-space(math.div(2,12), 1);

        @media (max-width: $tablet-lg) {
            margin-left: unset;
            margin-right: unset;
        }
    }

    &.-bigger {
        --container-margin: var(--container-margin-bigger);

        @media (max-width: $tablet-lg) {
            --container-margin: 20px;
        }
    }

    &.-wide {
        --container-margin: 20px;
    }
}

.main-content{
    background-color: var(--color-bg);
    min-height: vh(100);
}

// When admin is logged, change sticky position with Admin bar
body.admin-bar {
    .admin-gap{
        height: 32px;

        @media screen and (max-width: 782px) {
            top: 46px;
        }
    }
}

.btn-scroll {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 72px;
    height: 72px;
    padding: 16px;
    background-color: $lv-bleu;
    border-radius: 200px;
    margin-left: auto;
    margin-right: auto;
    transform: scale(1);
    transition: transform 0.3s ease-in-out;
    animation: bounce 2s infinite;

    .icon-arrow {
        color: $white;
        transform: rotate(270deg);
        font-size: rs(14px, 18px);
    }

    &.-white {
        background-color: $white;

        .icon-arrow {
            color: $black;
        }
    }
}