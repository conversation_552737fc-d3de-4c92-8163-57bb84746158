/**
  * This file contains code that overrides gutenberg blocks in admin and on pages
  * Gutenberg style must be inside theme.json : 
  * https://developer.wordpress.org/block-editor/how-to-guides/themes/global-settings-and-styles/
  * --------------------------------------------
  * But can be override here if necessary 
  */

.gutenberg, .wp-block-post-content {

  .titre-description-image-v2 {

    @media (max-width: $tablet) {
      flex-direction: column-reverse;
    }
  }

  // Full width
  .alignfull:not(.wp-block-image) {
    margin-right: calc(var(--container-margin) * -1);
    margin-left: calc(var(--container-margin) * -1);
    overflow: hidden;
  }

  // Make sure the image is always full height if parent column is set to stretch
  .is-vertically-aligned-stretch {
    .wp-block-image, img {
      height: 100%;
      object-fit: cover;
    }
  }

  .wp-block-cover {
    .wp-block-cover__inner-container > * { 
      padding-left: var(--container-margin) !important; 
      padding-right: var(--container-margin) !important;
    }
  }

  h2.wp-block-heading {
    margin-top: rs(64px, 104px);
    margin-bottom: rs(32px, 40px);
  }

  h3.wp-block-heading {
    margin-top: rs(40px, 72px);
    margin-bottom: rs(20px, 20px);
  }

  h4.wp-block-heading {
    margin-top: rs(40px, 40px);
    margin-bottom: rs(20px, 20px);
  }

  // fix 4 columns layout (break earlier)
  // .wp-container-core-columns-is-layout-4 {
  //   @media (max-width: $desktop-sm) and (min-width: 781px) {
  //     flex-wrap: wrap !important;
  //     .wp-block-column { 
  //       flex-basis: 40% !important; 
  //     }
  //   }
  // }

  // lists
  ul, ol {
    list-style: initial;
    padding: 0 1em;
    margin-left: rem(20px);

    li{
      margin: 1em 0;
      color: rgba(21, 21, 21, 0.70);

      &::marker {
        color: rgba(21, 21, 21, 0.70);
        font-size: 20px;
      }
    }
  }

  ol {
    list-style: number;
  }

  // Adjust columns
  .wp-block-columns {
    margin-top: responsive-size(40px, 80px);
    margin-bottom: responsive-size(40px, 80px);

    gap: grid-space(math.div(2,12), 0);
  }

  // fix 4 columns layout breaking on tablet
  // .wp-container-core-columns-is-layout-4 {
  //   @media (max-width: $desktop-sm) and (min-width: 781px) {
  //     flex-wrap: wrap !important;
  //     .wp-block-column { 
  //       flex-basis: 40% !important; 
  //     }
  //   }
  // }

  /**** Block Youtube ****/
  .wp-block-embed-youtube {
    //max-width: 992px;
    margin: 0px auto;

    .wp-block-embed__wrapper {
      position: relative;
      padding-bottom: 56.25%; /* 16:9 */
      padding-top: 25px;
      height: 0;

      iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }
    }

    @media (max-width: 1500px) {
      max-width: 992px;
    }
  }

  .has-large-font-size { 
  }

  .has-regular-font-size { 
  }

  .has-small-font-size { 
  }

  .has-smaller-font-size { 
  }

  // Table
  .wp-block-table {
    table {
      border-radius: $radius-xs;
      overflow: hidden;
    }

    td, th {
      border: none;
      padding: responsive-size(10px, 20px);
      font-size: responsive-size(14px, 16px);
      line-height: responsive-size(18px, 24px);;
    }

    tr {
      th:first-child, td:first-child{
        padding-left: responsive-size(15px, 40px);
      }

      th:last-child, td:last-child {
        padding-right: responsive-size(15px, 40px);
      }
    }

    thead {
      background: $black;

      tr {

      }

      th {
        @extend h6;
        color: $white;
        text-align: start;

        @media (max-width: $tablet) {
          padding-top: 20px;
          padding-bottom: 20px;
        }
      }
    }

    tbody {
      tr {
        background: $white;

        &:nth-child(odd) {
          background: $light-grey;
        }
      }

      td {

      }
    }
  }

  // Button
  .is-style-primary .wp-block-button__link {
    @extend .-primary;
  }
  
  .is-style-secondary .wp-block-button__link {
    @extend .-secondary;
  }

  // Quote
  .wp-block-quote {
    margin: responsive-size(80px, 140px) 0;
    padding-left: rem(50px);

    p {
      @include flex($align: start, $gap: 60px);
      color: $dark-grey;
      font-family: var(--font-title);
      font-size: responsive-size(26px, 32px);
      line-height: responsive-size(38px, 46px);;
      font-style: italic;
      letter-spacing: -1.2px;
    }

    @media (max-width: $tablet-lg) {
      p {
        flex-direction: column;
      }
    }
  }

  // Accordion (wordpress native)
  // .wp-block-details {
  //   position: relative;
  //   border-bottom: 1px solid $black;

  //   summary {
  //     @extend h5;
  //     padding: 32px 32px 32px 0;
  //     margin: 0;
  //     display: flex;
  //     transition: $transition;
      
  //     &::marker {
  //       display: none;
  //     }
      
  //     &::before {
  //       @include icon('\e909'); // carret icon
  //       transition: $transition;
  //       font-size: responsive-size(12px, 12px);
  //       right: 0;
  //     }
  //   }


  //   // all child except summary
  //   *:not(summary) {
  //     margin-block-start: 0em;
  //     margin-block-end: 2em;
  //   }

  //   // open
  //   &[open] {
  //     summary {
  //       color: $black;
        
  //       &::before {
  //         transform: rotate(180deg);
  //       }
  //     }
  //   }
  // }

  // // // // // 
  // Pattern
  // // // // //

  // Add each pattern custom class here

  .group-list {
    border-radius: rem(32px);
    padding: rem(56px) rem(40px);

    @media (max-width: $tablet) {
      padding: rem(20px);
    }

    .title {
      margin-bottom: rem(56px);
    }

    p {
      border-top: 1px solid rgba($black, 0.4);
      padding: rem(20px) 0;
      margin-bottom: rem(20px);
      color: rgba(21, 21, 21, 0.70);
      display: flex;
      gap: 40px;
      align-items: center;

      @media (max-width: $tablet) {
        gap: rem(20px);
      }

      &:last-child {
        margin-bottom: 0;
      }

      &:before {
        font-size: rem(40px);
        font-family: 'icomoon';
        content: '\e907';
        color: $black;
      }
    }
  }

  .group-color {
    border-radius: rem(24px);
    padding: rem(40px) rem(32px);
  }

  .group-list.has-lv-bleu-bg-background-color, .group-color.has-lv-bleu-bg-background-color {
    padding-bottom: 5.5rem;

    h2.wp-block-heading {
      margin-top: 0;
    }
  }

  p a:not(.wp-element-button):not(.wp-block-button__link) {
    display: inline;
    white-space: normal;
    word-break: normal;
    overflow-wrap: anywhere;
  }
}

.page-template-approch-page .gutenberg {
  h2.wp-block-heading,
  h3.wp-block-heading,
  h4.wp-block-heading {
    margin-top: 0;
  }
}

.wp-block-spacer.-no-mobile {
  @media (max-width: $tablet-lg) {
    display: none;
  }
}