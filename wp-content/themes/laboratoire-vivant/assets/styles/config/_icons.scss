/* Replace with Generated icomoon file
** https://icomoon.io/app/#/select
** Fonts files (.eot/.svg/ttf...) must be in fonts folder
*/

[class^="icon-"], [class*=" icon-"] {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'icomoon' !important;
    speak: never;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;

    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-arrow-export:before {
  content: "\e909";
}
.icon-check:before {
  content: "\e907";
}
.icon-list:before {
  content: "\e908";
}
.icon-document:before {
  content: "\e903";
}
.icon-user-share:before {
  content: "\e904";
}
.icon-handshake:before {
  content: "\e905";
}
.icon-data:before {
  content: "\e906";
}
.icon-arrow-simple:before {
  content: "\e900";
}
.icon-plus:before {
  content: "\e901";
}
.icon-arrow:before {
  content: "\e902";
}
