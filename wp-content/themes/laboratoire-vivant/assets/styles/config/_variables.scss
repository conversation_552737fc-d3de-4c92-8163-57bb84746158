/* Set Variable to :root DOM */
:root {
    // Colors
    --color-text: #{$dark-grey};
    --color-title: #{$black};
    --color-bg: #{$white};

    --primary: #{$primary};

    // Font family
    --font-primary : "DM Sans", sans-serif;
    --font-title : "DM Sans", sans-serif;

    // Font sizes
    --font-size-large: #{responsive-size(22px, 26px, $desktop-lg)};
    --font-size-body:  #{responsive-size(20px, 22px, $desktop-lg)};
    --font-size-small: #{responsive-size(16px, 18px, $desktop-lg)};
    --font-size-xsmall: #{responsive-size(13px, 15px, $desktop-lg)};

    // Line heights
    --line-height-large:    #{responsive-size(32px, 38px, $desktop-lg)};
    --line-height-body:     #{responsive-size(30px, 32px, $desktop-lg)};
    --line-height-small:    #{responsive-size(24px, 28px, $desktop-lg)};
    --line-height-xsmall:   #{responsive-size(16px, 20px, $desktop-lg)};

    --font-size-h1-display: #{responsive-size(36px, 80px, $desktop-lg)};
    --font-size-h1:       #{responsive-size(46px, 72px, $desktop-lg)};
    // --font-size-h1-small: #{responsive-size(40px, 60px, $desktop-lg)};
    --font-size-h2-xlarge: #{responsive-size(44px, 68px, $desktop-lg)};
    --font-size-h2:       #{responsive-size(40px, 48px, $desktop-lg)}; 
    --font-size-h2-large: #{responsive-size(44px, 56px, $desktop-lg)};
    --font-size-h2:       #{responsive-size(40px, 48px, $desktop-lg)}; 
    --font-size-h2-small: #{responsive-size(38px, 44px, $desktop-lg)};
    --font-size-h3:       #{responsive-size(32px, 40px, $desktop-lg)};
    --font-size-h3-small: #{responsive-size(28px, 36px, $desktop-lg)};
    --font-size-h4:       #{responsive-size(26px, 32px, $desktop-lg)};
    --font-size-h5:       #{responsive-size(24px, 26px, $desktop-lg)};
    --font-size-h6:       #{responsive-size(20px, 24px, $desktop-lg)};

    --line-height-h1-display: #{responsive-size(50px, 88px, $desktop-lg)};
    --line-height-h1:       #{responsive-size(48px, 80px, $desktop-lg)};
    // --line-height-h1-small: #{responsive-size(60px, 120px, $desktop-lg)};
    --line-height-h2-large: #{responsive-size(52px, 68px, $desktop-lg)};
    --line-height-h2:       #{responsive-size(46px, 58px, $desktop-lg)};
    --line-height-h2-small: #{responsive-size(46px, 54px, $desktop-lg)};
    --line-height-h3:       #{responsive-size(38px, 50px, $desktop-lg)};
    --line-height-h3-small: #{responsive-size(34px, 44px, $desktop-lg)};
    --line-height-h4:       #{responsive-size(36px, 40px, $desktop-lg)};
    --line-height-h5:       #{responsive-size(30px, 32px, $desktop-lg)};
    --line-height-h6:       #{responsive-size(24px, 30px, $desktop-lg)};

    // Link
    --color-link: #{$dark-grey};
    --color-link-hover: #{$black};

    // Btn
    --color-btn-primary: #{$black};
    --color-btn-secondary: transparent;
    --color-btn-text: #{$white};
    --color-btn-text-secondary: #{$black};

    --btn-size: #{responsive-size(18px, 20px, $desktop-lg)};

    // CTA
    --font-size-cta-large:  #{responsive-size(18px, 20px, $desktop-lg)};
    --font-size-cta:        #{responsive-size(20px, 24px, $desktop-lg)};
    
    --line-height-cta-large: #{responsive-size(20px, 24px, $desktop-lg)};
    --line-height-cta:      #{responsive-size(24px, 30px, $desktop-lg)};


    // Tags 
    // --font-size-tag-large: 15px;
    --font-size-tag: 12px;

    // --line-height-tag-large: 32px;
    --line-height-tag: 32px;

    // Surtitle
    --font-size-eyebrow: #{responsive-size(28px, 36px, $desktop-lg)};
    --line-height-eyebrow: #{responsive-size(40px, 50px, $desktop-lg)};

    // Gutenberg pacing
    --spacing-small: 40px;
    --spacing-normal: 64px;
    --spacing-medium: 80px;
    --spacing-large: 120px;
    
    // Metrics
    --unit: #{$column-gap};
    --container-margin: 56px;
    --container-margin-bigger: 120px;

    // Grid
    --grid-columns: #{$base-column-nb};
    --grid-gutter: var(--unit);
    --grid-margin: var(--container-margin);

    // Set a max-width for the container (useful for large screens)
    @media screen and (min-width: $desktop-xxlg) {
        --container-margin: 10vw;
    }

    @media screen and (max-width: $desktop-sm) {
        --container-margin: 50px;
    }

    @media screen and (max-width: $tablet) {
        --unit: 20px;
        --grid-columns: 4;

        --container-margin: 20px;

        --spacing-small: 10px;
        --spacing-normal: 30px;
        --spacing-medium: 60px;
        --spacing-large: 100px;
    }
}