footer {
    @include container;
    padding-bottom: 20px;
    background-color: $white;
    border-radius: 40px;
    
    .footer-menu {
        padding: 0;
        margin: 0;
        list-style: none;
        background-color: $white;

        .primary-item {
            margin: 0;
            padding: 0;

            > a {
                display: block;
                @extend .-h5;
                margin-bottom: 32px;

                @media (max-width: $tablet-lg) {
                    margin-bottom: 20px;
                }
            }

            ul.sub-menu {
                margin: 0;
                padding: 0;
                list-style: none;

                .menu-item {
                    margin-bottom: 8px;

                    &:last-child {
                        margin-bottom: 0;
                    }

                    a {
                        display: block;
                        color: #454545;
                        @extend .--xsmall;
                        transition: transform 0.3s ease-in-out;

                        &:hover {
                            transform: translateX(10px);
                        }
                    }
                }
            }
        }
    }

    .footer-logos {
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        margin-top: 10px;
        margin-bottom: 60px;

        @media (max-width: $desktop-sm) {
            flex-direction: column;
            align-items: center;
            gap: 64px;
            margin-top: 64px;
        }

        .footer-first-logos {
            display: flex;
            align-items: center;
            gap: 65px;

            @media (max-width: $desktop-sm) {
                flex-direction: column;
                align-items: center;
                gap: 40px;
            }

            .logo-plq {
                @media (max-width: $desktop-sm) {
                    width: 132px;
                }
            }
            
            .logo-novalait {
                @media (max-width: $desktop-sm) {
                    width: 158px;
                }
            }            

            img {
                width: 100%;
            }
        }

        .footer-second-logos {
            display: flex;
            align-items: flex-end;
            justify-content: flex-end;

            @media (max-width: $desktop-sm) {
                flex-direction: column;
                align-items: center;
            }

            .logo-lvlc {
                @media (max-width: $desktop-sm) {
                    width: 222px;
                }
            }

            img {
                width: 100%;
            }
        }
    }

    .end-menu {
        display: flex;
        align-items: center;
        justify-content: space-between;

        @media (max-width: $tablet-lg) {
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .copyright {
            @extend .--xsmall;
            display: flex;
            align-items: center;
            gap: 32px;

            @media (max-width: $tablet-lg) {
                flex-wrap: wrap;
                justify-content: center;
                gap: 8px;
            }

            span {
                @media (max-width: $tablet-lg) {
                    text-align: center;
                    width: 100%;
                }
            }
        }

        .site-web {
            @extend .--xsmall;
        }
    }

    .footer-newsletter {
        padding: 140px 0;
        border-bottom: 1px solid rgba($color: $black, $alpha: 0.3);
        margin-bottom: 64px;
        align-items: flex-start;

        @media (max-width: $tablet-lg) {
            padding-bottom: 0;
            border-bottom: none;
        }

        &-icon {
            img {
                width: 100%;

                @media (max-width: $tablet-lg) {
                    height: 140px;
                }

                @media (max-width: $tablet) {
                    height: 95px;
                }
            }
        }

        &-title {
            padding-right: grid-space(math.div(1,12), 1);

            @media (max-width: $tablet-lg) {    
                padding-right: 0;
            }

            &-text {
                @extend .-h3;
                margin: 0;
            }
        }

        &-content {

            &-description-text {
                @extend .--small;
                margin-top: 0;
                margin-bottom: 45px;

                @media (max-width: $tablet-lg) {
                    margin-bottom: 32px;
                }
            }

            &-form {
                display: flex;
                align-items: center;
                gap: 16px;

                @media (max-width: $tablet-lg) {
                    flex-direction: column;
                }

                input {
                    @extend .--normal;
                    border-radius: 80px;
                    border: 1px solid $black;
                    padding: 16px 24px;
                    width: 100%;
                }

                .main-button {
                    width: 35%;

                    @media (max-width: $tablet-lg) {
                        width: fit-content;
                        margin-right: auto;
                    }
                }
                
            }
        }
    }
}

// Hide WPML stuff
.otgs-development-site-front-end,
.wpml-ls-statics-footer { display: none !important; }