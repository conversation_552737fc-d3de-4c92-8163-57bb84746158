header {
  @include container;
  padding: rem(32) var(--grid-margin);
  background-color: var(--header-color);
  display: flex;
  align-items: flex-start;
  justify-content: space-between;

  .header-content {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
  }

  .logo {

    img.-black {
      display: none;
    }

    img.-white {
      display: block;
    }
  }

  &.-black {
    .logo {
      img.-black {
        display: block;
      }

      img.-white {
        display: none;
      }
    }
  }

  .primary-menu {
    display: flex;
    gap: 8px 16px;
    flex-wrap: wrap;

    &.-desktop {
      @media (max-width: $tablet-lg) {
        display: none;
      }
    }

    &.-mobile {
      @media (min-width: $tablet-lg) {
        display: none;
      }
    }

    li {
      width: calc(50% - 8px);

      a {
        display: block;
        @extend .--small;
        line-height: normal;
        font-weight: 500;
      }

      &.-hide-desktop {
        @media (min-width: $tablet-lg) {
          display: none;
        }
      }

      // &.current-menu-item{
      //   a{
      //     text-decoration: underline $black;
      //     text-underline-offset: 3px;
      //     text-decoration-thickness: 2px;
      //   }
      // }

    }
  }

  .contact-button {
    @media (max-width: $tablet-lg) {
      display: none;
    }

    a {
      display: block;
      border-radius: 100px;
      border: 2px solid $black;
      padding: 6px 16px;
      @extend .--small;
      line-height: normal;
      font-weight: 500;
      text-decoration: none;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .mobile-menu-button {
    display: none;
    @media (max-width: $tablet-lg) {
      display: block;
    }
  }
  

  .top-header{
    width: 100%;
    gap: var(--grid-gutter);
    display: flex;
    justify-content: space-between;
  }
  

  // .lang-switcher{
  //   grid-column-start: 3;
  //   grid-column-end: 4;
  //   text-align: right;

  //   .wpml-ls,.wpml-ls-link{
  //     padding: 0;
  //     text-transform: uppercase;
  //   }

  //   .wpml-ls-link:hover{
  //     text-decoration: underline;
  //   }
  // }

  .mobile-menu {
    background-color: $lv-bleu-ligne;
    padding: 64px 40px;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: auto;
    min-height: vh(100);
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    overflow: auto;
    padding-bottom: calc(40px + env(safe-area-inset-bottom));
    opacity: 0;
    visibility: hidden;
    transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
    opacity 0.3s ease-in-out,
    visibility 0.3s ease-in-out;

    // Responsive padding
    @media (max-width: 600px) {
      padding: 32px 16px calc(16px + env(safe-area-inset-bottom));
    }
    @media (max-width: 400px) {
      padding: 16px 8px calc(8px + env(safe-area-inset-bottom));
    }

    &.-open {
      transform: translateX(0);
      opacity: 1;
      visibility: visible;
    }

    .mobile-menu-close {
      position: absolute;
      top: calc(24px + env(safe-area-inset-top));
      right: calc(24px + env(safe-area-inset-right));
      opacity: 0;
      transform: scale(0.8);
      transition: opacity 0.3s ease-in-out, 
                  transform 0.3s ease-in-out;
      @media (max-width: 600px) {
        top: calc(12px + env(safe-area-inset-top));
        right: calc(12px + env(safe-area-inset-right));
      }
    }
    
    &.-open .mobile-menu-close {
      opacity: 1;
      transform: scale(1);
      transition-delay: 0.2s;
    }
    
    .primary-menu {
      display: flex;
      flex-direction: column;
      flex: 1 1 auto;
      justify-content: center;
      gap: 6vh;
      @media (max-width: 900px) {
        gap: 4vh;
      }
      @media (max-width: 600px) {
        gap: 3vh;
      }
      @media (max-width: 400px) {
        gap: 2vh;
      }

      li {
        width: 100%;
        opacity: 0;
        transform: translateY(20px);
        transition: opacity 0.4s ease-in-out, 
                    transform 0.4s ease-in-out;

        // Animation delay for each menu item - same for opening and closing
        @for $i from 1 through 10 {
          &:nth-child(#{$i}) {
            transition-delay: #{0.1 + ($i * 0.05)}s;
          }
        }

        a {
          display: block;
          font-size: 32px;
          font-weight: 500;
          line-height: normal;
          letter-spacing: -0.64px;
          color: $white;
          transition: color 0.3s ease-in-out;
          
          &:hover {
            color: rgba(255, 255, 255, 0.8);
          }
        }
      }
    }

    &.-open .primary-menu li {
      opacity: 1;
      transform: translateY(0);
    }

    .mobile-menu-logo-container {
      margin: 0 auto 0;
      text-align: center;
      align-self: center;
      opacity: 0;
      transform: translateY(20px);
      transition: opacity 0.4s ease-in-out, 
                  transform 0.4s ease-in-out;
      padding-bottom: 3vh;
    }

    &.-open .mobile-menu-logo-container {
      opacity: 1;
      transform: translateY(0);
      transition-delay: 0.3s;
    }
  }
}