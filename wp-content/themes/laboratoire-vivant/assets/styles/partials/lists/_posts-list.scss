.posts-list {
    gap: 0;

    .article {
        cursor: pointer;
        min-height: 560px;
        padding: 40px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: flex-start;
        transition: $transition;
        @include articleBorders();

        &:hover {
            box-shadow: 0px 18.182px 36.364px 0px rgba(0, 0, 0, 0.15);

            & ~ .article {
                opacity: 0.6;
            }

            .image {
                filter: grayscale(50%);

                img {
                    transform: scale(1.05);
                }
            }
        }

        .header {
            width: 100%;
        }
    }

    .article:has(~ .article:hover) {
        opacity: 0.6;
    }
    
    .image {
        overflow: hidden;
        border-radius: $radius-xs;

        img { 
            transition: $transition;
            @include img(); 
        }
    }

    @media (max-width: $tablet-lg) {
        .article {
            min-height: 420px;
            padding: rem(40px) rem(32px);
        }
    }
}