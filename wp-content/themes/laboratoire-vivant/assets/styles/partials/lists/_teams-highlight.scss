.team-highlight-ctn{
    padding-bottom: rs(160px, 200px);

    .intro{
        margin-bottom: rem(64px);
        .title{
            margin: 0 0 rs(20px, 40px);
        }
    }
}


.teams-highlight{
    list-style: none;
    @include grid(2, rem(20px));

    .item{
        position: relative;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: center;
        border-top: 1px solid $black-40;
        border-left: 1px solid $black-40;
        width: 100%;
        padding: rem(32px);
        min-height: grid-space(math.div(3,12), 0);
        transition: all 0.4s cubic-bezier(0.77, 0, 0.175, 1);

        a{
            width: 100%;

        }

        .number{
            display: block;
            font-size: rs(56px, 80px);
            font-weight: 700;
            line-height: 120%;
            letter-spacing: rs(-6px, -7px);

            //fix overflow hidden
            .word-mask{
                padding-right: 5px;
            }
        }

        .text{
            color: $black-70;
            display: block;
            // margin-top: rem(24px);
            font-size: rs(18px, 24px);
        }

        img{
            display: block;
            margin: rem(24px) 0 0 auto;
            height: rem(100px);
            opacity: 0;
            transform: scale(0.92);
            filter: blur(5px);
            transition: opacity 0.6s cubic-bezier(0.77, 0, 0.175, 1) , transform 0.6s cubic-bezier(0.77, 0, 0.175, 1), filter 0.6s cubic-bezier(0.77, 0, 0.175, 1);
        }

        &:hover,&:has(a:focus-visible) {
            border-color: transparent;
            background-color: $lv-vert-accent;
            border-radius: rem(24px);
            cursor: pointer;

            img{
                opacity: 1;
                transform: scale(1);
                filter: blur(0);
            }

            &:nth-child(1){
                background-color: $lv-vert-accent;
            }

            &:nth-child(2){
                background-color: $lv-jaune-accent;
            }

            &:nth-child(3){
                background-color: $lv-bleu-accent;
            }

            &:nth-child(4){
                background-color: $lv-orange-accent;
            }
        }
    }

    @media (max-width: $tablet) {
        @include grid(1, 0);

        .item{
            min-height: auto;
            padding: rem(24px) 0 rem(16px);
            border-left: 0;
            &:last-child{
                border-bottom: 1px solid $black-40;
            }

            a{
                display: flex;
                align-items: center;
                gap: var(--grid-gutter);
                position: relative;

                &:after{
                    position: absolute;
                    content: "\e90b";
                    font-family: 'icomoon';
                    font-size: rs(10px, 14px);
                    right: rem(32px);
                    top: 50%;
                    transform: translateY(-50%);

                }

                .text{
                    max-width: grid-space(math.div(2,4));
                }

                .number{
                    text-align: center;
                    width: grid-space(math.div(1,4));
                }
            }

            img{
                display: none;
            }

            &:hover,&:has(a:focus-visible) {
                border-color: $black-40;
                background-color: transparent;
                border-radius: 0;
            }
        }
    }
}