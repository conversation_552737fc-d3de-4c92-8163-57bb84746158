.actions-fields-card {
    border-radius: $radius-l;
    padding: rs(28px, 40px);
    width: 100%;
    height: auto;
    cursor: pointer;
    aspect-ratio: 1/1;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    gap: rs(28px, 40px);
    transition: opacity 0.3s ease-in-out, filter 0.3s ease-in-out, box-shadow 0.3s ease-in-out ;

    .content {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        gap: rs(16px, 24px);
        height: 100%;
        width: calc(100% - 88px);
    }

    .title {
        @extend .-h3;
        margin: 0;

        a {
            color: var(--color-title);
        }

        @media (max-width: $tablet-lg) {
            font-size: var(--font-size-h4);
            line-height: var(--line-height-h4);
        }
    }

    .description{
        margin: 0;
        margin-top: rem(40px);
    }

    .illustration{
        transform: scale(1);
        transition: transform 0.6s ease-in-out;
    }

    .link-wrapper {
        width: 48px;
        height: 48px;
    }

    .link {
        width: 100%;
        height: 100%;
        background-color: $white;
        border-radius: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: transform 0.3s ease-in-out;

        span {
            font-size: rs(10px, 12px);
        }
    }

    &.-reverse {
        .content {
            flex-direction: column-reverse;
        }
    }

    &.-inactive {
        opacity: 0.8;
        // filter: grayscale(0.5);
    }

    &.-bigger{
        aspect-ratio: unset;
        padding: rs(40px, 56px);
        padding-top: rs(64px, 80x);
        min-height: rs(640px, 800px);
        width: 100%;
        height: 100%;

        .content{
            height: 100%;
            width: 100%;
        }

        .title{
            @extend .-h2;
            margin: 0;
        }

        .description{
            margin: rem(28px) 0;
        }

        .illustration{
            width: 70%;
            margin-left: auto;

            @media (max-width: $tablet-lg) {
                width: 100%;
                text-align: center;
                margin-left: 0;
            }

            img{
                width: 100%;
            }
        }

        @media (max-width: $tablet-lg) {
            aspect-ratio: unset;
            height: 100%;

            .content{
                flex-direction: column-reverse;
            }
        }
    }
    

    &:hover {

        box-shadow: 0px 18.182px 36.364px 0px rgba(0, 0, 0, 0.15);

        .link {
            transform: scale(1.2);
        }

        .illustration{
            transform: scale(1.04);
        }
    }
}