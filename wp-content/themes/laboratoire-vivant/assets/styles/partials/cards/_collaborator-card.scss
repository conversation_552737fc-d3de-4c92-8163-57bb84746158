
.collaborator {
    width: 100%;
    border-top: 1px solid $black-40;
    border-left: 1px solid $black-40;
    border-right: 1px solid transparent;
    border-bottom: 1px solid transparent;
    padding: 20px 32px;
    box-shadow: none;
    transition: $transition;

    @media (min-width: $tablet-lg) {
        &.has-link {
            cursor: pointer;

            &:hover{
                border-color: $black-40;
                box-shadow: 0px 18.182px 36.364px 0px rgba(0, 0, 0, 0.15);

                .collaborator-image {
                    img {
                        transform: scale(1.05);
                    }
                }
            }
        }
    }

    .collaborator-image {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        overflow: hidden;
        margin-bottom: 24px;
        flex-shrink: 0;

        @media (max-width: $tablet-lg) {
            margin-bottom: 0;
            margin-right: 24px;
            width: rem(100px);
            height: rem(100px);
            aspect-ratio: 1/1;
        }

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transform: scale(1);
            transition: $transition;
        }
    }

    .collaborator-content {

        @media (max-width: $tablet-lg) {
            width: calc(100% - 120px);
        }
    }

    .collaborator-name {
        @extend .-h5;
        margin: 0;

        @media (max-width: $tablet-lg) {
            margin-top: 0;
        }
    }

    .collaborator-description {
        margin: rem(10px) 0 0;
        @extend .--small;
    }

    &.-big {
        padding: 64px 40px;

        @media (max-width: $tablet-lg) {
            &:not(.-always) {
                padding: 40px 0;
            }
        }

        .collaborator-image {
            width: 235px;
            height: 235px;
            border-radius: 300px;
            margin-bottom: 95px;

            @media (max-width: $tablet-lg) {
                width: 105px;
                height: 105px;
                margin-bottom: 0;   
            }
        }


        .collaborator-name {
            margin-top: 0;
            margin-bottom: rem(16px);
            @extend .-h3;
            
            @media (max-width: $mobile) {
                font-size: var(--font-size-h5);
                line-height: var(--line-height-h5);
                margin-bottom: 0;
            }
        }

        &.-always {

            @media (max-width: $tablet-lg) {
                padding: 40px;
            }

            .collaborator-image {
                @media (max-width: $tablet-lg) {
                    width: 190px;
                    height: 190px;
                }
            }

            .collaborator-content {

                @media (max-width: $tablet-lg) {
                    margin-top: 24px;
                    width: 100%;
                }
            }
        }
    }

    &.-smaller{
        padding: rem(20px);

        .collaborator-image {
            width: rem(96px);
            height: rem(96px);
        }
    }

    @media (max-width: $tablet-lg) {
        &:not(.-always) {
            padding: 20px;
            display: flex;
            align-items: center;
            border-left: none;
        }
    }

}