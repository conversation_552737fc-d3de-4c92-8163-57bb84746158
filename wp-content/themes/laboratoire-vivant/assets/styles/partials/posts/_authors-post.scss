.authors-post {
    display: flex;
    align-items: center;
    justify-content: flex-start;

    .author {
        display: flex;
        align-items: center;

        .author-image {
            width: 65px;
            height: 65px;
            border-radius: 100px;
            overflow: hidden;
            margin-right: 24px;
            border: 5px solid $white;
            flex-shrink: 0;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }

        .author-name {
            @extend .--xsmall;
            color: $black;
            width: 60%;
        }
    }

    &.-multiple {

        .author {

            .author-image {
                margin-right: 0;
                margin-left: -15px;
                border: 5px solid $white;
            }

            &:first-child {

                .author-image {
                    margin-left: 0;
                }
            }
        }
    }
}