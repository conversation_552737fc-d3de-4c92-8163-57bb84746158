.page-hero {
    position: relative;
    width: 100%;
    padding: 170px 0;

    .top-content {
        position: relative;
        text-align: center;

        .content {
            position: relative;
            z-index: 1;
        }

        .surtitle {
            color: $lv-bleu;
        }

        .title {
            margin: var(--spacing-small) 0;
        }

        .btn-scroll {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 72px;
            height: 72px;
            transform: rotate(270deg);
            padding: 16px;
            background-color: $lv-bleu;
            border-radius: 200px;
            margin-left: auto;
            margin-right: auto;
            transition: transform 0.3s ease-in-out;

            .icon-arrow {
                color: $white;
                font-size: rs(18px, 14px);
            }

            &:hover {
                transform: translateY(10px) rotate(270deg);
            }
        }

        .cloud-wrapper {
            position: absolute;
            z-index: 0;

            &.-left {
                left: -50px;
                bottom: 50px;
            }

            &.-right {
                right: -40px;
                top: -40px;
            }
        }
    }

    .illustration {
        overflow: hidden;

        img {
            width: 100%;
            min-width: 1440px; // Assuming this is the full desktop width
            position: relative;
            left: 50%;
            transform: translateX(-50%);
        }
    }

    .slide-in-left {

        &.-show {
            transition-delay: 1.5s;
        }
    }   

    .slide-in-right {

        &.-show {
            transition-delay: 1.5s;
        }
    }
}

.page-template-approch-page {

    .top-content {

        .btn-scroll {
            background-color: $white;

            .icon-arrow {
                color: $black;
            }
        }
    }
}