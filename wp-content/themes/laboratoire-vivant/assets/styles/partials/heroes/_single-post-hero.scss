.single-post-hero {
    position: relative;
    width: 100%;
    height: 710px;
    border-radius: 0px 0px $radius-xl $radius-xl;
    background: $lv-bleu-bg;

    .content {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: rem(95px);
    }

    .category-color {
        @extend .-eyebrow;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 16px;
        margin-bottom: 40px;

        .circle {
            width: 16px;
            height: 16px;
            aspect-ratio: 1/1;
            border-radius: 100px;
        }
    }

    .title {
        text-align: center;
        margin: 0 auto;
        margin-bottom: 40px;
    }
}