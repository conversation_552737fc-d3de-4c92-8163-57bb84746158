.title-illustration-hero {
    background-color: var(--header-color);
    border-radius: 0px 0px 40px 40px;
    min-height: 360px;

    @media (max-width: $tablet-lg) {
        min-height: auto;
    }

    .container {
        padding-bottom: rem(40px);

        @media (max-width: $tablet-lg) {
            padding-bottom: 0;
        }

        .title-wrapper {
            display: flex;
            align-items: flex-end;
            justify-content: flex-start;
            padding-bottom: rem(40px);
            
            @media (max-width: $tablet-lg) {
                order: 2;
                padding-bottom: 0;
                margin: rem(65px) 0;
            }

            .title {
                margin: 0;
            }
        }

        .illustration {
            width: 100%;

            @media (max-width: $tablet-lg) {
                order: 1;
            }

            .illustration-image {
                width: 100%;
                height: 100%;
            }
        }

        &.team-container {
            padding-bottom: 0;

            .title-wrapper {

                @media (max-width: $tablet-lg) {
                    margin-bottom: 0;
                }
            }
        }
    } 
    
    .team-wrapper {
        margin-top: rem(50px);

        @media (max-width: $tablet-lg) {
            padding-bottom: rem(55px);
        }

        .team-list {
            list-style: none;
            padding: 0;
            margin: 0;

            .team-item {
                text-align: center;
                cursor: pointer;
                position: relative;

                @media (max-width: $tablet-lg) {
                    text-align: left;
                }

                

                .team-link {
                    color: #525252;
                    margin-bottom: 0;

                    @media (max-width: $mobile) {
                        font-size: rem(24px);
                    }
                }

                &::after {
                    content: '';
                    display: block;
                    width: 0;
                    height: 6px;
                    border-radius: 100px;
                    background-color: $lv-vert;
                    position: absolute;
                    bottom: -40px;
                    left: 50%;
                    transform: translateX(-50%);
                    transition: width 0.3s ease-in-out;

                    @media (max-width: $tablet-lg) {
                        display: none;
                    }
                }

                &.active,
                &:hover {

                    .team-link {
                        color: $lv-vert;
                        font-weight: 700;

                        @media (max-width: $tablet-lg) {
                            border-bottom: 3px solid $lv-vert;
                        }
                    }

                    &::after {
                        width: 70%;
                    }
                }
            }
        }
    }
}