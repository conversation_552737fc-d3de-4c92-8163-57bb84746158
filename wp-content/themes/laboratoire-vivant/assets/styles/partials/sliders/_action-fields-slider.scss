.action-fields-slider {

    // to fix issue with swiper and container margin, make slide disappear on the sides not container sides
    margin-right: calc(var(--container-margin) * -1) !important;
    margin-left: calc(var(--container-margin) * -1) !important;
    overflow: hidden;
    padding: 0 var(--container-margin);
    max-width: unset !important;

    .swiper-slide {
        border-radius: $radius-l;
        padding: rs(40px, 28px);
        height: auto;
        cursor: pointer;
        aspect-ratio: 1/1;
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        gap: rs(40px, 28px);

        &.-reverse {
            .content {
                flex-direction: column-reverse;
            }
        }

        .content {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            gap: rs(24px, 16px);
            height: 100%;
            width: calc(100% - 88px);
        }

        .title {
            @extend .-h3;
            margin: 0;
        }

        .link-wrapper {
            width: 48px;
            height: 48px;
        }

        .link {
            width: 100%;
            height: 100%;
            background-color: $white;
            border-radius: 100px;
            display: flex;
            align-items: center;
            justify-content: center;

            span {
                font-size: rs(12px, 10px);
                transition: transform 0.3s ease-in-out;
            }
        }

        &:hover {
            .link {
                span {
                    transform: translate(5px, -5px);
                }
            }
        }
    }

    .swiper-navigation {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 8px;
        margin-top: rs(64px, 40px);

        .prev,
        .next {
            width: rs(72px, 56px);
            height: rs(72px, 56px);
            border-radius: 50%;
            background-color: $black;
            display: flex;
            align-items: center;
            justify-content: center;
            color: $white;
            font-size: rs(24px, 20px);
            font-weight: 700;
            cursor: pointer;

            span {
                transition: transform 0.3s ease-in-out;
            }
        }

        .next {

            &:hover {
                span {
                    transform: translateX(5px);
                }
            }
        }

        .prev {
            transform: rotate(180deg);

            &:hover {
                span {
                    transform: translateX(5px);
                }
            }
        }
    }
}