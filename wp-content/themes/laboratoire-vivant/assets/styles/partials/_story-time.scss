.story-time-ctn{
    position: relative;
    height: 600vh;
    z-index: 1;
    pointer-events: none;


    .story-line{
        width: 100%;
        position: absolute;
        min-height: 100vh;
        @include flex($gap:grid-space(math.div(1,12), 2));
        align-items: center;
        pointer-events: auto;
        

        &.-invert{
            flex-direction: row-reverse;
        }
        
        &.-center{
            flex-direction: column;
            gap:0;

            h2{
                max-width: 820px;
                text-align: center;
                margin-bottom: 30px;
            }
        }

        .title{
            width: min(rem(440px), 100%);
        }

        .image{
            flex-shrink: 0;

            img{
                max-width: 100%;
                height: auto;
            }
        }        
    }

    @media (max-width: $tablet-lg) {

        height: 400vh;
        overflow: hidden;

        .story-line{
            flex-direction: column-reverse;

            .image{
                max-width: 90%;
                margin: 0 auto;
            }

            .title{
                text-align: center;
                margin: 0;
            }

            &.-invert{
                flex-direction: column-reverse;
            }
        }
    }
}