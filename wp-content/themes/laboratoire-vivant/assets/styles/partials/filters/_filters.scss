.filters-wrapper {
    .filters-ctn {
        margin-bottom: 56px;
        width: 100%;

        .search-ctn {
            border-radius: 100px;
            overflow: hidden;
            background-color: transparent;
            display: flex;
            align-items: center;
            box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.20);
            margin-left: auto;
            position: relative;

            &:has(input:focus-visible) {
                outline-color: #0071BC;
                outline-style: solid;
                outline-width: 1px;
                outline-offset: 0.2em;

                input { outline: none; }
            }
            
            input {
                width: rem(440px);
                padding: 14px 0 15px 24px;
                border: none;
                text-overflow: ellipsis;
                background: transparent;
                @extend .--normal;
                color: #555;
            }

            i {
                padding-right: 24px;
                font-size: rem(40px);
                position: relative;
                color: #8A8A8A;
            }

            @media (max-width: $desktop-sm) {
                margin-left: unset;
            }

            @media (max-width: $tablet) {
                width: 100%;

                input {
                    width: 100%;
                }
            }

            .search-btn {
                cursor: pointer;
                display: flex;
                align-items: center;
            }

            .clear {
                position: absolute;
                right: 60px;
                cursor: pointer;

                &.-hide {
                    display: none;
                }

                i {
                    font-size: 15px;
                    display: block;
                    padding-right: 0;
                    transform: rotate(45deg);
                }
            }
        }

        .filters {

            .filter-radio {
                display: flex;
                align-items: center;
                justify-content: flex-start;
                gap: 64px;
                padding-left: 20px;
                border: none; // Retirer la bordure par défaut du fieldset
                margin: 0; // Retirer la marge par défaut du fieldset

                @media (max-width: $tablet-lg) {
                    margin-top: rem(42px);
                    gap: 32px;
                    padding-left: 0;
                }

                .option {
                    
                    // Cacher les inputs radio
                    input[type="radio"] {
                        display: none;
                        
                        // Amélioration de l'accessibilité pour la navigation au clavier
                        &:focus-visible + label {
                            color: $black;
                            
                            &:before {
                                width: 100%;
                                outline: 2px solid $black;
                                outline-offset: 2px;
                            }
                        }
                        
                        &:focus + label {
                            color: $black;
                            
                            &:before {
                                width: 100%;
                                outline: 2px solid $black;
                                outline-offset: 2px;
                            }
                        }
                    }
                    
                    // Styles pour les labels (boutons)
                    label {
                        font-size: responsive-size(18px, 22px, $desktop);
                        font-weight: 600;
                        line-height: 150%;
                        letter-spacing: responsive-size(-0.27px, -0.33px, $desktop);
                        color: rgba(35, 31, 32, 0.70);
                        cursor: pointer;
                        transition: all 0.3s ease;
                        height: 50px;
                        position: relative;

                        &:before {
                            content: '';
                            display: block;
                            width: 0;
                            height: 4px;
                            background: #231F20;
                            transition: all 0.3s ease;
                            position: absolute;
                            bottom: -10px;
                            left: 0;
                            border-radius: 100px;
                        }
                        
                        // Amélioration de l'accessibilité pour les labels focusables
                        &:focus-visible {
                            outline: 2px solid $black;
                            outline-offset: 2px;
                            border-radius: 4px;
                        }
                    }
                    
                    // État selected (quand le radio est checked)
                    input[type="radio"]:checked + label, label:hover {
                        color: $black;

                        &:before {
                            width: 100%;
                        }
                    }
                }
            }
        }

        .filter-group {

            .label {
                display: flex;
                padding: 16px 24px;
                justify-content: center;
                align-items: center;
                gap: 10px;
                border-radius: 100px;
                border: 2px solid #231F20;
                color: $black;
                @extend .-cta-large;
                font-weight: 600;
                cursor: pointer;
                transition: transform 0.3s ease;
                
                &:hover {
                    transform: scale(1.05);
                }
                
                // Amélioration de l'accessibilité pour la navigation au clavier
                &:focus-visible {
                    outline: 2px solid $black;
                    outline-offset: 2px;
                    transform: scale(1.05);
                }
            }

            &.-open {
                .filters-popup {
                    @extend .-open;
                }
            }
        }
    }

    .selected-row {
        margin-top: rem(40px);
        display: flex;
        align-items: center;
        justify-content: flex-start;
        flex-wrap: wrap;
        gap: 20px;

        button {
            width: fit-content;
            display: flex;
            padding: 16px 24px;
            justify-content: center;
            align-items: center;
            gap: 10px;
            border-radius: 100px;
            border: 2px solid $lv-bleu-bg;
            background-color: $lv-bleu-bg;
            @extend .-cta;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease;
            
            .icon-plus {
                transform: rotate(45deg);
            }

            &:hover {
                transform: scale(1.05);
            }
            
            // Amélioration de l'accessibilité pour la navigation au clavier
            &:focus-visible {
                outline: 2px solid $black;
                outline-offset: 2px;
                transform: scale(1.05);
            }
        }
    }
}
