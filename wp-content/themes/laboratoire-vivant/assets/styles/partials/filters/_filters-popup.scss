.filters-popup {
    position: fixed;
    width: 100vw;
    height: 100vh;
    visibility: hidden;
    top: 0;
    left: 0;
    background: rgba(3, 61, 86, 0.40);
    z-index: -1;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: opacity 0.4s ease, visibility 0s ease 0.5s, z-index 0s ease 0.5s;
    opacity: 0;

    .overlay{
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
    }

    .wrapper {
        width: grid-space(math.div(6,12), 2);
        margin: 0 auto;
        background-color: $white;
        border-radius: 24px;
        // margin-top: rem(200px);
        position: relative;
        overflow: hidden;
        transition: transform 0s ease 0.6s;
        transform: translateY(rem(20px));
        z-index: 2;
    }

    // .btn.-close{
    //     position: absolute;
    //     width: rem(60px);
    //     height: rem(60px);
    //     background-color: white;
    //     top: 0;
    //     right: 0;
    //     padding: 0;
    //     color: $blue;
    //     font-size: rem(22px);
    //     transform: translate(50%, -50%);
    //     z-index: 3;
    //     &:focus-visible {
    //         outline-color: white;
    //     }
    // }

    .top {
        display: flex;
        justify-content: space-between;
        padding: 25px 24px;
        background-color: $white;
        box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.16);
        border-radius: 24px 24px 0px 0px;

        p { 
            margin: 15px 0;
        }

        button {
            color: $black;
            i {
                display: block;
                transform: rotate(45deg);
                font-size: 20px;
            }
            cursor: pointer;
            
            // Amélioration de l'accessibilité pour le bouton de fermeture
            &:focus-visible {
                outline: 2px solid $black;
                outline-offset: 2px;
                border-radius: 4px;
            }
        }
    }

    .options {
        padding: 11px 0;
        overflow-y: scroll;
        height: vh(50);

        .option {
            padding: 16px 24px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.10);

            &:last-child {
                border-bottom: none;
            }
            
            &:hover label {
                color: $black !important;
            }

            label {
                font-size: rem(18px);
                font-weight: 700;
                color: $black !important;
                transition: color 0.4s ease;
            }

            // hide input
            input[type="checkbox"] {
                position: absolute;
                height: 0;
                width: 0;
                outline: none;

                &:focus-visible {

                    & + label {
                        color: $black !important;

                        &::before{
                            outline: 2px solid $black;
                            outline-offset: 2px;
                        }
                    }
                }
                
                // Amélioration pour la navigation au clavier
                &:focus {
                    & + label {
                        color: $black !important;
                        
                        &::before {
                            outline: 2px solid $black;
                            outline-offset: 2px;
                        }
                    }
                }
            }

            // Custom checkbox
            input[type="checkbox"] + label {
                position: relative;
                padding-left: 44px;
                cursor: pointer;
                display: block;
                color: $black !important;
                font-size: rs(16px,20px);
                line-height: 150%;
                font-weight: 600;
                font-family: var(--font-title);

                &:before {
                    @include flex();
                    content: "\e90d";
                    font-family: 'icomoon';
                    position: absolute;
                    left: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 24px;
                    height: 24px;
                    border-radius: 8px;
                    color: transparent;
                    font-size: 12px;
                    border: 2px solid $black;
                    transition: background-color 0.3s ease-in-out, border-color 0.3s ease-in-out;
                }
                
                // Amélioration de l'accessibilité pour les labels focusables
                &:focus-visible {
                    outline: 2px solid $black;
                    outline-offset: 2px;
                    border-radius: 4px;
                }
            }

            input[type="checkbox"]:checked + label::before {
                background-color: $lv-jaune-accent !important;
                border-color: $lv-jaune-accent !important;
                color: $black;

                &:after {
                    content: "";
                    position: absolute;
                    left: 4px;
                    top: 8px;
                    width: 10px;
                    height: 10px;
                }
            }

            // Label checked
            input[type="checkbox"]:checked + label {
                color: $black !important;
            }
        }
    }

    .bottom {
        padding: 24px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: $white;
        border-radius: 0px 0px 24px 24px;
        box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.25);
    }

    &.-open{
        visibility: visible;
        z-index: 9999;
        opacity: 1;
        transition: opacity 0.4s ease 0.1s, visibility 0.1s ease;
        
        .wrapper {
            transition: transform 0.4s ease;
            transform: translateY(0);
        }
    }

    @media (max-width: $tablet-lg) {
        position: fixed;
        align-items: flex-end;
        .wrapper {
            width: 100%;
            margin-left: 0;
            margin-right: 0;
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 0;
        }

        .bottom{
            border-radius: 0px;
        }

        // .btn.-close{
        //     transform: translate(20%, -20%);
        // }
    }
}