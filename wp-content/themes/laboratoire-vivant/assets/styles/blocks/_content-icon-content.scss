.icons-content-wrapper {
    gap: 55px;

    .icons-content {
        border-top: 1px solid rgba($black, 0.50);
        padding: 40px 0;
        display: flex;
        align-items: flex-start;
        gap: 40px 70px;

        @media (max-width: $desktop-lg) {
            flex-direction: column;
            align-items: flex-start;
            gap: 20px;
        }

        &__icon {
            color: $lv-bleu-ligne;
            font-size: 80px;

            &.check {
                font-size: 40px;
            }
        }

        &__content {
            @extend .--normal;
            margin: 0!important;
        }
        
    }
}

.wp-block-column {
    .icons-content-wrapper {
        gap: 0;

        .icons-content {
            grid-column-end: span 12;
            gap: 40px;

            &__icon {
                font-size: 65px;
            }
        }
    }
}