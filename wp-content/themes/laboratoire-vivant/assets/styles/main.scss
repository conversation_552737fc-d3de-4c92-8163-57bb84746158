@use 'sass:math';

@import 'commons.scss';

// 1. Import node modules Vendors css
@import '../../node_modules/swiper/swiper.scss';
@import '../../node_modules/swiper/modules/autoplay.scss';
// @import '../../node_modules/swiper/modules/effect-fade.scss';

// 2. Import fonts and icons
@import 'config/fonts';
@import 'config/icons';

// -------- HELPERS --------
@import 'utilities/helpers';
@import 'utilities/development-banner';

// 3. Set global page layout and containers, typography and links/buttons
@import 'common/grid';
@import 'common/typography';

// 4. <PERSON><PERSON> gutenberg blocks custom styling
@import 'common/gutenberg';
@import 'common/forms';
@import 'common/ajax';
@import 'common/loader';

// -------- PARTIALS --------

// Components
@import 'partials/header/header';
@import 'partials/footer/footer';
@import 'partials/story-time';

// Commons
@import 'partials/commons/pagination';
@import 'partials/commons/preloader';
@import 'partials/commons/share-post';

// Cards
// @import 'partials/cards/sample-card';

// Filters
@import 'partials/filters/filters';
@import 'partials/filters/filters-popup';

// Heroes
@import 'partials/heroes/page-hero';
@import 'partials/heroes/home-hero';

// Lists
@import 'partials/lists/teams-highlight';

// Sliders
// @import 'partials/sliders/sample-slider';
@import 'partials/sliders/action-fields-slider';

// Snippets
@import 'partials/snippets/links';
@import 'partials/snippets/hamburger';
@import 'partials/snippets/mouse-tracker';
@import 'partials/snippets/mask-title';


// Blocks
@import 'blocks/content-accordion';
@import 'blocks/content-title-content';
@import 'blocks/content-icon-content';
@import 'blocks/content-slider';

// -------- PAGES --------
@import 'pages/home';
@import 'pages/404';
@import 'pages/approch-page';

// -------- SINGLES --------
// @import 'pages/singles/...';



// -------- EXTRA --------

// 98. Style override for page printing 
@import 'common/print';

// 99. This file contains hacky code to eventually put elsewhere, load it at the very end
@import 'common/shame';