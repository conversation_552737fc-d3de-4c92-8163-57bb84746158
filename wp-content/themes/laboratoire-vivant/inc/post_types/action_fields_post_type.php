<?php

/**
 * Add action fields custom post type
 */
function action_fields_custom_post_type()
{
    $labels = array(
        'name'                     => 'Champs d\'action',
        'singular_name'            => 'Champ d\'action',
        'all_items'                => 'Tous les champs d\'action',
        'add_new'                  => 'Ajouter',
        'add_new_item'             => 'Ajouter un champ d\'action',
        'edit_item'                => 'Modifier le champ d\'action',
        'new_item'                 => 'Nouveau champ d\'action',
        'view_item'                => 'Voir le champ d\'action',
        'search_items'             => 'Rechercher un champ d\'action',
        'not_found'                => 'Aucun champ d\'action trouvé',
        'not_found_in_trash'       => 'Aucun champ d\'action trouvé dans la corbeille',
        'menu_name'                => 'Champs d\'action',
        'item_published'           => 'Champ d\'action publié',
        'item_published_privately' => 'Champ d\'action publié en privé',
        'item_scheduled'           => 'Champ d\'action planifié',
        'item_updated'             => 'Champ d\'action mis à jour',
    );
    $args = array(
        'labels'              => $labels,
        'hierarchical'        => false,
        'supports'            => array('title', 'editor', 'thumbnail'),
        'public'              => true,
        'show_ui'             => true,
        'show_in_menu'        => true,
        'show_in_rest'        => true,
        'menu_position'       => null,
        'menu_icon'           => 'dashicons-category',
        'show_in_nav_menus'   => true,
        'show_in_admin_bar'   => true,
        'publicly_queryable'  => true,
        'exclude_from_search' => false,
        'has_archive'         => true,
        'query_var'           => true,
        'can_export'          => true,
        'rewrite'             => array('slug' => __('champ-action', 'laboratoire-vivant'), 'with_front' => true),
    );
    register_post_type('action_fields', $args);
}
