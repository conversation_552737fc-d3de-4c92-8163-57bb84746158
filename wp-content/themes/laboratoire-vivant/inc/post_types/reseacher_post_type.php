<?php

/**
 * Add researcher custom post type
 */
function researcher_custom_post_type()
{
    // Admin labels (back office)
    $labels = array(
        'name'                     => 'Chercheurs',
        'singular_name'            => 'Chercheur',
        'all_items'                => 'Tous les chercheurs',
        'add_new'                  => 'Ajouter',
        'add_new_item'             => 'Ajouter un chercheur',
        'edit_item'                => 'Modifier le chercheur',
        'new_item'                 => 'Nouveau chercheur',
        'view_item'                => 'Voir le chercheur',
        'search_items'             => 'Rechercher un chercheur',
        'not_found'                => 'Aucun chercheur trouvé',
        'not_found_in_trash'       => 'Aucun chercheur trouvé dans la corbeille',
        'menu_name'                => 'Chercheurs',
        'item_published'           => 'Chercheur publié',
        'item_published_privately' => 'Chercheur publié en privé',
        'item_scheduled'           => 'Chercheur planifié',
        'item_updated'             => 'Chercheur mis à jour',
    );

    // Frontend labels (affichage sur le site)
    $frontend_labels = array(
        'name'          => 'Recherche',
        'singular_name' => 'Recherche',
    );

    // Merge frontend labels for public-facing strings
    $args = array(
        'labels'              => array_merge($labels, $frontend_labels),
        'hierarchical'        => false,
        'supports'            => array('title', 'editor', 'thumbnail'),
        'public'              => true,
        'show_ui'             => true,
        'show_in_menu'        => true,
        'show_in_rest'        => true,
        'menu_position'       => null,
        'menu_icon'           => 'dashicons-search',
        'show_in_nav_menus'   => true,
        'show_in_admin_bar'   => true,
        'publicly_queryable'  => true,
        'exclude_from_search' => false,
        'has_archive'         => true,
        'query_var'           => true,
        'can_export'          => true,
        'rewrite'             => array('slug' => __('researcher', 'laboratoire-vivant'), 'with_front' => true),
    );
    register_post_type('researcher', $args);
}
