<?php

/**
 * Add coordinator custom post type
 */
function coordinator_custom_post_type()
{
    // Back office labels (admin)
    $labels = array(
        'name'                     => 'Coordinateurs',
        'singular_name'            => 'Coordinateur',
        'all_items'                => 'Tous les coordinateurs',
        'add_new'                  => 'Ajouter',
        'add_new_item'             => 'Ajouter un coordinateur',
        'edit_item'                => 'Modifier le coordinateur',
        'new_item'                 => 'Nouveau coordinateur',
        'view_item'                => 'Voir le coordinateur',
        'search_items'             => 'Rechercher un coordinateur',
        'not_found'                => 'Aucune coordinateur trouvé',
        'not_found_in_trash'       => 'Aucune coordinateur trouvé dans la corbeille',
        'menu_name'                => 'Coordinateurs',
        'item_published'           => 'Coordinateur publié',
        'item_published_privately' => 'Coordinateur publié en privé',
        'item_scheduled'           => 'Coordinateur planifié',
        'item_updated'             => 'Coordinateur mis à jour',
    );

    // Frontend labels (site)
    $frontend_labels = array(
        'name'          => 'Coordination',
        'singular_name' => 'Coordination',
    );

    // Merge frontend labels for public-facing strings
    $args = array(
        'labels'              => array_merge($labels, $frontend_labels),
        'hierarchical'        => false,
        'supports'            => array('title', 'editor', 'thumbnail'),
        'public'              => true,
        'show_ui'             => true,
        'show_in_menu'        => true,
        'show_in_rest'        => true,
        'menu_position'       => null,
        'menu_icon'           => 'dashicons-groups',
        'show_in_nav_menus'   => true,
        'show_in_admin_bar'   => true,
        'publicly_queryable'  => true,
        'exclude_from_search' => false,
        'has_archive'         => true,
        'query_var'           => true,
        'can_export'          => true,
        'rewrite'             => array('slug' => __('coordinateur', 'laboratoire-vivant'), 'with_front' => true),
    );
    register_post_type('coordinator', $args);
}
