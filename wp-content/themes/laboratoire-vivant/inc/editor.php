<?php
/**
 * Disable <PERSON><PERSON><PERSON> by template
 *
 */
function disable_gutenberg( $can_edit ) {
	if ( ! ( is_admin() && ! empty( $_GET['post'] ) ) ) {
		return $can_edit;
	}
	if ( should_disable( $_GET['post'] ) ) {
		$can_edit = false;
	}

	return $can_edit;
}

function gutenberg_allowed_block_types( $allowed_blocks ) {

	return array(
		'core/image',
		'core/paragraph',
		'core/heading',
		'core/list',
		'core/list-item',
		'core/gallery',
		'core/quote',
		'core/cover',
		'core/file',
		'core/video',
		'core/table',
		'core/code',
		'core/freeform',
		'core/html',
		'core/preformatted',
		'core/button',
		'core/buttons',
		'core/column',
		'core/columns',
		'core/text-columns',
		'core/media-text',
		'core/separator',
		'core/embed',
		'core/spacer',
		'core/shortcode',
		'core/group',
		'core/embed/youtube',
		'core/details',

		// ACF 
		'acf/slider',
		'acf/accordion',
		'acf/title-content',
		'acf/icon-content',

		// GF
		// 'gravityforms/form',
	);
}

/**
 * Templates and Page IDs without editor
 *
 */
function should_disable( $id = false ) {
	if ( empty( $id ) ) {
		return false;
	}
	$disabled_ids = [
		//get_option( 'page_on_front' ),
	];
	$disabled_types = [
		//'council_member',
	];
	$disabled_templates = [
		//'templates/landing.php',
	];

	return in_array( intval( $id ), $disabled_ids ) || in_array( get_post_type( $id ), $disabled_types ) || in_array( get_page_template_slug( $id ), $disabled_templates );
}


/**
 * Add custom patterns
 *
 */
function kryzaplate_register_patterns() {

	remove_theme_support( 'core-block-patterns' );

	register_block_pattern_category(
		'laboratoire-vivant',
		array( 'label' => __( 'laboratoire-vivant', 'laboratoire-vivant' ) )
	);

	register_block_pattern(
		'laboratoire-vivant/titre-description-image-v1',
		array(
			'title'       => __( 'Titre/Description/Image v1', 'laboratoire-vivant' ),
			'description' => _x( 'Titre/Description/Image', 'Block pattern description', 'laboratoire-vivant' ),
			'categories'  => array( 'laboratoire-vivant' ),
			'content' 	  => '
				<!-- wp:columns {"verticalAlignment":"center","className":"titre-description-image-v1"} -->
				<div class="wp-block-columns are-vertically-aligned-center titre-description-image-v1"><!-- wp:column {"verticalAlignment":"center"} -->
				<div class="wp-block-column is-vertically-aligned-center"><!-- wp:image {"id":49,"sizeSlug":"full","linkDestination":"none","align":"full"} -->
				<figure class="wp-block-image alignfull size-full"><img src="https://laboratoire-vivant.local/wp-content/uploads/2025/06/demarche-vision.svg" alt="" class="wp-image-49"/></figure>
				<!-- /wp:image --></div>
				<!-- /wp:column -->

				<!-- wp:column {"verticalAlignment":"center"} -->
				<div class="wp-block-column is-vertically-aligned-center"><!-- wp:heading {"style":{"spacing":{"margin":{"top":"0","bottom":"var:preset|spacing|normal","left":"0","right":"0"},"padding":{"top":"0","bottom":"0","left":"0","right":"0"}}}} -->
				<h2 class="wp-block-heading" style="margin-top:0;margin-right:0;margin-bottom:var(--wp--preset--spacing--normal);margin-left:0;padding-top:0;padding-right:0;padding-bottom:0;padding-left:0">Une vision d’avenir bien ancrée dans le présent</h2>
				<!-- /wp:heading -->

				<!-- wp:paragraph {"style":{"spacing":{"padding":{"top":"0","bottom":"0"},"margin":{"top":"0","bottom":"0"}}}} -->
				<p style="margin-top:0;margin-bottom:0;padding-top:0;padding-bottom:0">Bien que déjà engagés depuis plusieurs années vers l’adoption de pratiques durables, les producteurs laitiers québécois ont souhaité aller encore plus loin et se doter d’un cadre scientifique pour pouvoir comprendre, expérimenter, innover et partager leurs apprentissages.</p>
				<!-- /wp:paragraph --></div>
				<!-- /wp:column --></div>
				<!-- /wp:columns -->',
		)
	);

	register_block_pattern(
		'laboratoire-vivant/titre-description-image-v2',
		array(
			'title'       => __( 'Titre/Description/Image v2', 'laboratoire-vivant' ),
			'description' => _x( 'Titre/Description/Image', 'Block pattern description', 'laboratoire-vivant' ),
			'categories'  => array( 'laboratoire-vivant' ),
			'content' 	  => '
				<!-- wp:columns {"verticalAlignment":"center","className":"titre-description-image-v2"} -->
				<div class="wp-block-columns are-vertically-aligned-center titre-description-image-v2"><!-- wp:column {"verticalAlignment":"center"} -->
				<div class="wp-block-column is-vertically-aligned-center"><!-- wp:heading {"style":{"spacing":{"margin":{"top":"0","bottom":"var:preset|spacing|normal","left":"0","right":"0"},"padding":{"top":"0","bottom":"0","left":"0","right":"0"}}}} -->
				<h2 class="wp-block-heading" style="margin-top:0;margin-right:0;margin-bottom:var(--wp--preset--spacing--normal);margin-left:0;padding-top:0;padding-right:0;padding-bottom:0;padding-left:0">Une démarche d’innovation supportée par la recherche</h2>
				<!-- /wp:heading -->

				<!-- wp:paragraph {"style":{"spacing":{"padding":{"top":"0","bottom":"0"},"margin":{"top":"0","bottom":"0"}}}} -->
				<p style="margin-top:0;margin-bottom:0;padding-top:0;padding-bottom:0">Le Laboratoire vivant – Lait carboneutre vise à favoriser l’adoption de pratiques agricoles de réduction des gaz à effet de serre et de séquestration de carbone sur les fermes laitières québécoises et canadiennes par une approche collaborative et innovante entre producteurs, chercheurs et experts.</p>
				<!-- /wp:paragraph --></div>
				<!-- /wp:column -->

				<!-- wp:column {"verticalAlignment":"center"} -->
				<div class="wp-block-column is-vertically-aligned-center"><!-- wp:image {"id":39,"sizeSlug":"full","linkDestination":"none","align":"full"} -->
				<figure class="wp-block-image alignfull size-full"><img src="https://laboratoire-vivant.local/wp-content/uploads/2025/06/demarche-innovation.svg" alt="" class="wp-image-39"/></figure>
				<!-- /wp:image --></div>
				<!-- /wp:column --></div>
				<!-- /wp:columns -->',
		)
	);

	register_block_pattern(
		'laboratoire-vivant/group-list',
		array(
			'title'       => __( 'Groupe de liste', 'laboratoire-vivant' ),
			'description' => _x( 'Groupe de liste', 'Block pattern description', 'laboratoire-vivant' ),
			'categories'  => array( 'laboratoire-vivant' ),
			'content' 	  => '
				<!-- wp:group {"className":"group-list","backgroundColor":"lv-bleu-bg","layout":{"type":"constrained","justifyContent":"left"}} -->
				<div class="wp-block-group group-list has-lv-bleu-bg-background-color has-background"><!-- wp:heading {"className":"title"} -->
				<h2 class="wp-block-heading title">Les recherches scientifiques</h2>
				<!-- /wp:heading -->

				<!-- wp:paragraph -->
				<p>Nom de la recherche #1</p>
				<!-- /wp:paragraph -->

				<!-- wp:paragraph -->
				<p>Nom de la recherche #2</p>
				<!-- /wp:paragraph -->

				<!-- wp:paragraph -->
				<p>Nom de la recherche #3</p>
				<!-- /wp:paragraph --></div>
				<!-- /wp:group -->',
		)
	);

	register_block_pattern(
		'laboratoire-vivant/group-color',
		array(
			'title'       => __( 'Groupe de couleur', 'laboratoire-vivant' ),
			'description' => _x( 'Groupe de couleur', 'Block pattern description', 'laboratoire-vivant' ),
			'categories'  => array( 'laboratoire-vivant' ),
			'content' 	  => '
				<!-- wp:group {"className":"group-color","backgroundColor":"lv-bleu-bg","layout":{"type":"constrained","justifyContent":"left"}} -->
				<div class="wp-block-group group-color has-lv-bleu-bg-background-color has-background"><!-- wp:paragraph -->
				<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc feugiat justo vel mauris venenatis, euismod volutpat ante eleifend. Pellentesque malesuada luctus turpis at lobortis. Cras et metus tempor, elementum lectus nec, laoreet mauris.</p>
				<!-- /wp:paragraph --></div>
				<!-- /wp:group -->',
		)
	);

	register_block_pattern(
		'laboratoire-vivant/group-two-columns-icons',
		array(
			'title'       => __( 'Groupe de deux colonnes avec icônes', 'laboratoire-vivant' ),
			'description' => _x( 'Groupe de deux colonnes avec icônes', 'Block pattern description', 'laboratoire-vivant' ),
			'categories'  => array( 'laboratoire-vivant' ),
			'content' 	  => '
				<!-- wp:group {"className":"two-columns-icons","layout":{"type":"constrained"}} -->
				<div class="wp-block-group two-columns-icons"><!-- wp:columns -->
				<div class="wp-block-columns"><!-- wp:column {"className":"-first-column"} -->
				<div class="wp-block-column -first-column"><!-- wp:group {"className":"-first-column-content","layout":{"type":"constrained"}} -->
				<div class="wp-block-group -first-column-content"><!-- wp:heading -->
				<h2 class="wp-block-heading">Ce qui distingue notre démarche</h2>
				<!-- /wp:heading -->

				<!-- wp:paragraph -->
				<p>Un laboratoire vivant est un modèle d’innovation sur le terrain permettant de concevoir des solutions concrètes (produits, services, outils, méthodes, etc.) en impliquant directement les personnes qui vont les utiliser, de façon à optimiser l’apprentissage et l’adoption de pratiques utiles et adaptées au milieu.</p>
				<!-- /wp:paragraph --></div>
				<!-- /wp:group --></div>
				<!-- /wp:column -->

				<!-- wp:column {"className":"-second-column"} -->
				<div class="wp-block-column -second-column"><!-- wp:heading {"level":4} -->
				<h4 class="wp-block-heading">Le Laboratoire vivant – Lait carboneutre se distingue sur les aspects suivants :</h4>
				<!-- /wp:heading -->

				<!-- wp:acf/icon-content {"name":"acf/icon-content","data":{"icons-contents_0_icon":"list","_icons-contents_0_icon":"field_685997f625e33","icons-contents_0_content":"Au lieu d’évaluer l’effet des pratiques de manière séparée, le Laboratoire a été conçu, à la demande des producteurs laitiers pour mesurer l’effet global de l’ensemble des pratiques sur le potentiel de réduction du bilan carbone à la ferme","_icons-contents_0_content":"field_685998b125e34","icons-contents_1_icon":"data","_icons-contents_1_icon":"field_685997f625e33","icons-contents_1_content":"En plus d’avoir été consultés lors de l’élaboration des protocoles de recherche, les producteurs participants peuvent proposer et tester eux-mêmes des initiatives adaptées à leurs besoins et ambitions","_icons-contents_1_content":"field_685998b125e34","icons-contents_2_icon":"user-share","_icons-contents_2_icon":"field_685997f625e33","icons-contents_2_content":"La démarche intègre un volet social visant à comprendre les facteurs favorables et les freins à l’adoption des pratiques favorables à la réduction du bilan carbone","_icons-contents_2_content":"field_685998b125e34","icons-contents":3,"_icons-contents":"field_685997c325e32"},"mode":"edit"} /--></div>
				<!-- /wp:column --></div>
				<!-- /wp:columns --></div>
				<!-- /wp:group -->',
		)
	);

	register_block_pattern(
		'laboratoire-vivant/custom-pattern-2',
		array(
			'title'       => __( 'Sample 2', 'laboratoire-vivant' ),
			'description' => _x( 'Sample custom pasten 2', 'Block pattern description', 'laboratoire-vivant' ),
			'categories'  => array( 'laboratoire-vivant' ),
			'content' 	  => '
				<!-- wp:group {"layout":{"type":"constrained"}} -->
				<div class="wp-block-group"><!-- wp:heading -->
				<h2 class="wp-block-heading">Titre</h2>
				<!-- /wp:heading -->
				
				<!-- wp:paragraph -->
				<p>lorem ipsum</p>
				<!-- /wp:paragraph --></div>
				<!-- /wp:group -->',
		)
	);
}

function add_data_attributes($block_content, $block) {

	global $post;
	$templates_cibles = ['templates/approch-page.php'];
	$class_to_add = 'slide-in';
    $data_attr = 'data-scroll';

	if (empty($block_content)) {
        return $block_content;
    }

	if (strpos($block_content, '-first-column') !== false) {
		return $block_content;
	}

	if (!in_array(get_page_template_slug($post), $templates_cibles, true)) {
		return $block_content;
	}	

	if (preg_match('/<[^>]+class=["\']([^"\']*)["\']/', $block_content, $matches)) {
        // add class to existing classes
        $new_content = preg_replace(
            '/(<[^>]+class=["\'])([^"\']*)(["\'])/',
            '$1$2 ' . $class_to_add . '$3',
            $block_content,
            1
        );
    } else {
        // add class to tag
        $new_content = preg_replace(
            '/<([a-z1-6]+)(\s|>)/',
            '<$1 class="' . $class_to_add . '"$2',
            $block_content,
            1
        );
    }

    // add data attribute
    $new_content = preg_replace(
        '/<([a-z1-6]+)([^>]*)>/',
        '<$1$2 ' . $data_attr . '>',
        $new_content,
        1
    );

    return $new_content;
}