<?php

/**
 * Redirect default post category archives to the Publications page
 * with the selected category term ID as a query parameter.
 */
function redirect_category_to_publications() {
	if ( is_admin() || wp_doing_ajax() ) {
		return;
	}

	if ( ! is_category() ) {
		return;
	}

	$queried_object = get_queried_object();
	if ( ! ( $queried_object instanceof WP_Term ) || $queried_object->taxonomy !== 'category' ) {
		return;
	}

	$options = get_fields( 'options' );
	if ( empty( $options ) || empty( $options['page_links'] ) || empty( $options['page_links']['publications'] ) ) {
		return;
	}

	$publications_page = $options['page_links']['publications'];
	$page_id = is_object( $publications_page ) && ! empty( $publications_page->ID ) ? $publications_page->ID : ( is_array( $publications_page ) && ! empty( $publications_page['ID'] ) ? $publications_page['ID'] : null );
	if ( ! $page_id ) {
		return;
	}

	$target_url = add_query_arg( 'category', $queried_object->term_id, get_permalink( $page_id ) );

	wp_safe_redirect( $target_url, 302 );
	exit;
}


